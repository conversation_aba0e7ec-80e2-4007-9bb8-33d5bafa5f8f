package com.stt.android.home.dashboardv2.usecase

import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.diary.GetVo2MaxStateRangesUseCase
import com.stt.android.domain.diary.Vo2MaxRange
import com.stt.android.domain.diary.Vo2MaxState
import com.stt.android.domain.user.Sex
import com.stt.android.utils.DateUtils
import javax.inject.Inject

internal class DashboardVo2MaxUseCase @Inject constructor(
    private val userSettingsController: UserSettingsController,
    private val getVo2MaxStateRangesUseCase: GetVo2MaxStateRangesUseCase,
) {
    suspend fun getStateAndRangeItemList(vo2Max: Float?): Pair<Vo2MaxState?, List<Vo2MaxRange>> {
        return getVo2MaxStateRangesUseCase(
            GetVo2MaxStateRangesUseCase.Params(
                isMale = userSettingsController.settings.gender == Sex.MALE,
                vo2Max = vo2Max,
                userAge = DateUtils.calculateAge(userSettingsController.settings.birthDate)
            )
        )
    }
}
