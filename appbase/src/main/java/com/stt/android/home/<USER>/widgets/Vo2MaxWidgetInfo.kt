package com.stt.android.home.dashboardv2.widgets

import androidx.annotation.StringRes
import com.stt.android.R
import com.stt.android.domain.diary.Vo2MaxRange
import com.stt.android.domain.diary.Vo2MaxState

internal data class Vo2MaxWidgetInfo(
    val latestVo2Max: Float?,
    val latestVo2MaxDate: Long?,
    val state: Vo2MaxState?,
    val rangeItemList: List<Vo2MaxRange>,
) : WidgetInfo

@get:StringRes
internal val Vo2MaxState.labelResId: Int
    get() = when (this) {
        Vo2MaxState.SUPERIOR -> R.string.dashboard_widget_max_vo2_state_superior
        Vo2MaxState.EXCELLENT -> R.string.dashboard_widget_max_vo2_state_excellent
        Vo2MaxState.GOOD -> R.string.dashboard_widget_max_vo2_state_goode
        Vo2MaxState.FAIR -> R.string.dashboard_widget_max_vo2_state_fair
        Vo2MaxState.POOR -> R.string.dashboard_widget_max_vo2_state_poor
        Vo2MaxState.VERY_POOR -> R.string.dashboard_widget_max_vo2_state_very_poor
    }
