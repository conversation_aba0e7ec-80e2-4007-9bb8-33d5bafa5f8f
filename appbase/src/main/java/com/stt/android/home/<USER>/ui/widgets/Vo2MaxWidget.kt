package com.stt.android.home.dashboardv2.ui.widgets

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.theme.vo2MaxZone1
import com.stt.android.compose.theme.vo2MaxZone2
import com.stt.android.compose.theme.vo2MaxZone3
import com.stt.android.domain.diary.Vo2MaxRange
import com.stt.android.domain.diary.Vo2MaxState
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.home.dashboardv2.ui.widgets.common.CommonChartWidget
import com.stt.android.home.dashboardv2.ui.widgets.common.vo2MaxMonthDayLabel
import com.stt.android.home.dashboardv2.widgets.Vo2MaxWidgetInfo
import com.stt.android.home.dashboardv2.widgets.labelResId
import com.stt.android.ui.utils.TextFormatter
import java.time.Year
import kotlin.math.roundToInt

@Composable
internal fun Vo2MaxWidget(
    widgetInfo: Vo2MaxWidgetInfo,
    editMode: Boolean,
    onClick: (() -> Unit)?,
    onLongClick: (() -> Unit)?,
    onRemoveClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val (subHeader, title, subtitle) = if (widgetInfo.latestVo2Max == null) {
        Triple(
            stringResource(R.string.widget_no_data_title),
            stringResource(R.string.widget_no_data_title),
            stringResource(R.string.widget_no_data_subtitle),
        )
    } else {
        Triple(
            widgetInfo.latestVo2MaxDate?.let { latestVo2MaxDate ->
                val vo2MaxLocaleDate = latestVo2MaxDate.toLocalDate()
                if (vo2MaxLocaleDate.year == Year.now().value) {
                    vo2MaxLocaleDate.vo2MaxMonthDayLabel
                } else {
                    TextFormatter.formatDate(context, latestVo2MaxDate, true)
                }
            }.orEmpty(),
            TextFormatter.formatVo2Max(widgetInfo.latestVo2Max),
            widgetInfo.state?.let { stringResource(it.labelResId) }.orEmpty(),
        )
    }
    CommonChartWidget(
        editMode = editMode,
        headerRes = R.string.dashboard_widget_title_max_vo2,
        iconRes = R.drawable.ic_dashboard_widget_max_vo2,
        colorRes = R.color.dashboard_widget_max_vo2,
        subheaderText = subHeader,
        titleText = AnnotatedString(title),
        subtitleText = subtitle,
        onClick = onClick,
        onLongClick = onLongClick,
        onRemoveClick = onRemoveClick,
        modifier = modifier,
    ) {
        Vo2MaxProgress(widgetInfo = widgetInfo)
    }
}

@Composable
internal fun Vo2MaxProgress(
    widgetInfo: Vo2MaxWidgetInfo,
    modifier: Modifier = Modifier,
) {
    val currentVo2 = widgetInfo.latestVo2Max
    val rangeItemList = widgetInfo.rangeItemList

    val minValue = remember(widgetInfo) {
        rangeItemList.first().minValue
    }

    val maxValue = remember(widgetInfo) {
        rangeItemList.last().maxValue
    }

    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Vo2MaxProgressBar(
            currentVo2 = currentVo2,
            vo2MaxState = widgetInfo.state,
            minValue = minValue,
            maxValue = maxValue,
            rangeItemList = rangeItemList,
        )

        Spacer(
            modifier = Modifier
                .height(MaterialTheme.spacing.xsmall)
                .fillMaxWidth()
        )

        Vo2MaxMarker(
            minValue = minValue,
            maxValue = maxValue,
            rangeItemList = rangeItemList,
        )
    }
}

@Composable
private fun Vo2MaxProgressBar(
    currentVo2: Float?,
    vo2MaxState: Vo2MaxState?,
    minValue: Int,
    maxValue: Int,
    rangeItemList: List<Vo2MaxRange>,
    modifier: Modifier = Modifier,
) {
    val rangeItems = remember(currentVo2) {
        rangeItemList
    }
    var indicatorOffsetX by remember { mutableFloatStateOf(0f) }

    val zone1NormalColor = MaterialTheme.colorScheme.vo2MaxZone1
    val zone1EmptyColor = MaterialTheme.colorScheme.nearWhite
    val zone2NormalColor = MaterialTheme.colorScheme.vo2MaxZone2
    val zone2EmptyColor = MaterialTheme.colorScheme.lightGrey
    val zone3NormalColor = MaterialTheme.colorScheme.vo2MaxZone3
    val zone3EmptyColor = MaterialTheme.colorScheme.cloudyGrey

    Box(
        modifier = modifier.fillMaxWidth()
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(18.dp)
                .clip(RoundedCornerShape(2.dp))
                .align(Alignment.Center)
        ) {
            val canvasWidth = size.width
            val canvasHeight = size.height
            val totalRange = (maxValue - minValue + 1).toFloat()
            var rangeOffsetX = 0.dp.toPx()
            val rangeOffsetY = 3.dp.toPx()
            val rangeHeight = 12.dp.toPx()
            val cursorWidth = 3.dp.toPx()
            val cursorGap = 2.dp.toPx()
            val cornerRadius = 2.dp.toPx()

            var calculatedIndicatorOffsetX = 0f
            var tempRangeOffsetX = 0f

            rangeItems.forEachIndexed { index, rangeItem ->
                val rangeLength = (rangeItem.maxValue - rangeItem.minValue + 1).toFloat()
                val rangeWidth = if (index == rangeItems.lastIndex) {
                    (canvasWidth - tempRangeOffsetX).coerceAtLeast(0f)
                } else {
                    (canvasWidth * rangeLength / totalRange).roundToInt().toFloat()
                }

                if (currentVo2 != null && rangeItem.contains(currentVo2)) {
                    val positionInRange =
                        (currentVo2 - rangeItem.minValue) / (rangeItem.maxValue - rangeItem.minValue + 1).toFloat()
                    calculatedIndicatorOffsetX = tempRangeOffsetX + (rangeWidth * positionInRange)
                }
                tempRangeOffsetX += rangeWidth
            }
            indicatorOffsetX = calculatedIndicatorOffsetX

            rangeItems.forEachIndexed { index, rangeItem ->
                val progressColor = when (rangeItem.state) {
                    Vo2MaxState.POOR, Vo2MaxState.VERY_POOR ->
                        if (currentVo2 == null) zone1EmptyColor else zone1NormalColor

                    Vo2MaxState.GOOD, Vo2MaxState.FAIR ->
                        if (currentVo2 == null) zone2EmptyColor else zone2NormalColor

                    Vo2MaxState.SUPERIOR, Vo2MaxState.EXCELLENT ->
                        if (currentVo2 == null) zone3EmptyColor else zone3NormalColor
                }

                val rangeLength = (rangeItem.maxValue - rangeItem.minValue + 1).toFloat()
                val rangeWidth = if (index == rangeItems.lastIndex) {
                    (canvasWidth - rangeOffsetX).coerceAtLeast(0f)
                } else {
                    (canvasWidth * rangeLength / totalRange).roundToInt().toFloat()
                }

                if (currentVo2 != null &&
                    indicatorOffsetX >= rangeOffsetX &&
                    indicatorOffsetX <= rangeOffsetX + rangeWidth
                ) {
                    val leftPartWidth =
                        (indicatorOffsetX - rangeOffsetX - cursorGap).coerceAtLeast(0f)
                    val rightPartStart = indicatorOffsetX + cursorWidth + cursorGap
                    val rightPartWidth =
                        (rangeOffsetX + rangeWidth - rightPartStart).coerceAtLeast(0f)
                    if (leftPartWidth > 0) {
                        when (index) {
                            0 -> drawPath(
                                path = Path().apply {
                                    addRoundRect(
                                        RoundRect(
                                            androidx.compose.ui.geometry.Rect(
                                                Offset(x = rangeOffsetX, y = rangeOffsetY),
                                                Size(width = leftPartWidth, height = rangeHeight)
                                            ),
                                            topLeft = CornerRadius(cornerRadius, cornerRadius),
                                            topRight = CornerRadius(0f, 0f),
                                            bottomRight = CornerRadius(0f, 0f),
                                            bottomLeft = CornerRadius(cornerRadius, cornerRadius)
                                        )
                                    )
                                },
                                color = progressColor
                            )
                            rangeItems.lastIndex -> drawPath(
                                path = Path().apply {
                                    addRoundRect(
                                        RoundRect(
                                            androidx.compose.ui.geometry.Rect(
                                                Offset(x = rangeOffsetX, y = rangeOffsetY),
                                                Size(width = leftPartWidth, height = rangeHeight)
                                            ),
                                            topLeft = CornerRadius(0f, 0f),
                                            topRight = CornerRadius(cornerRadius, cornerRadius),
                                            bottomRight = CornerRadius(cornerRadius, cornerRadius),
                                            bottomLeft = CornerRadius(0f, 0f)
                                        )
                                    )
                                },
                                color = progressColor
                            )
                            else -> drawRect(
                                color = progressColor,
                                topLeft = Offset(x = rangeOffsetX, y = rangeOffsetY),
                                size = Size(width = leftPartWidth, height = rangeHeight)
                            )
                        }
                    }
                    if (rightPartWidth > 0) {
                        when (index) {
                            0 -> drawPath(
                                path = Path().apply {
                                    addRoundRect(
                                        RoundRect(
                                            androidx.compose.ui.geometry.Rect(
                                                Offset(x = rightPartStart, y = rangeOffsetY),
                                                Size(width = rightPartWidth, height = rangeHeight)
                                            ),
                                            topLeft = CornerRadius(cornerRadius, cornerRadius),
                                            topRight = CornerRadius(0f, 0f),
                                            bottomRight = CornerRadius(0f, 0f),
                                            bottomLeft = CornerRadius(cornerRadius, cornerRadius)
                                        )
                                    )
                                },
                                color = progressColor
                            )
                            rangeItems.lastIndex -> drawPath(
                                path = Path().apply {
                                    addRoundRect(
                                        RoundRect(
                                            androidx.compose.ui.geometry.Rect(
                                                Offset(x = rightPartStart, y = rangeOffsetY),
                                                Size(width = rightPartWidth, height = rangeHeight)
                                            ),
                                            topLeft = CornerRadius(0f, 0f),
                                            topRight = CornerRadius(cornerRadius, cornerRadius),
                                            bottomRight = CornerRadius(cornerRadius, cornerRadius),
                                            bottomLeft = CornerRadius(0f, 0f)
                                        )
                                    )
                                },
                                color = progressColor
                            )
                            else -> drawRect(
                                color = progressColor,
                                topLeft = Offset(x = rightPartStart, y = rangeOffsetY),
                                size = Size(width = rightPartWidth, height = rangeHeight)
                            )
                        }
                    }
                } else {
                    when (index) {
                        0 -> drawPath(
                            path = Path().apply {
                                addRoundRect(
                                    RoundRect(
                                        androidx.compose.ui.geometry.Rect(
                                            Offset(x = rangeOffsetX, y = rangeOffsetY),
                                            Size(width = rangeWidth, height = rangeHeight)
                                        ),
                                        topLeft = CornerRadius(cornerRadius, cornerRadius),
                                        topRight = CornerRadius(0f, 0f),
                                        bottomRight = CornerRadius(0f, 0f),
                                        bottomLeft = CornerRadius(cornerRadius, cornerRadius)
                                    )
                                )
                            },
                            color = progressColor
                        )
                        rangeItems.lastIndex -> drawPath(
                            path = Path().apply {
                                addRoundRect(
                                    RoundRect(
                                        androidx.compose.ui.geometry.Rect(
                                            Offset(x = rangeOffsetX, y = rangeOffsetY),
                                            Size(width = rangeWidth, height = rangeHeight)
                                        ),
                                        topLeft = CornerRadius(0f, 0f),
                                        topRight = CornerRadius(cornerRadius, cornerRadius),
                                        bottomRight = CornerRadius(cornerRadius, cornerRadius),
                                        bottomLeft = CornerRadius(0f, 0f)
                                    )
                                )
                            },
                            color = progressColor
                        )
                        else -> drawRect(
                            color = progressColor,
                            topLeft = Offset(x = rangeOffsetX, y = rangeOffsetY),
                            size = Size(width = rangeWidth, height = rangeHeight)
                        )
                    }
                }
                rangeOffsetX += rangeWidth
            }

            if (currentVo2 != null && vo2MaxState != null) {
                val cursorBgColor = when (vo2MaxState) {
                    Vo2MaxState.POOR, Vo2MaxState.VERY_POOR -> zone1NormalColor
                    Vo2MaxState.GOOD, Vo2MaxState.FAIR -> zone2NormalColor
                    Vo2MaxState.SUPERIOR, Vo2MaxState.EXCELLENT -> zone3NormalColor
                }

                drawRoundRect(
                    color = cursorBgColor,
                    topLeft = Offset(x = indicatorOffsetX, y = 0f),
                    size = Size(width = cursorWidth, height = canvasHeight),
                    cornerRadius = CornerRadius(cornerRadius)
                )
            }
        }
    }
}

@Composable
private fun Vo2MaxMarker(
    minValue: Int,
    maxValue: Int,
    rangeItemList: List<Vo2MaxRange>,
    modifier: Modifier = Modifier,
) {
    val textMeasurer = rememberTextMeasurer()
    val gray = MaterialTheme.colorScheme.mediumGrey
    val textStyle = MaterialTheme.typography.bodySmall.merge(gray)
    val markerLineHeight = LocalDensity.current.run { 3.dp.toPx() }
    val markerLabelSpace = LocalDensity.current.run { 8.dp.toPx() }

    Box(modifier = modifier.fillMaxWidth()) {
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .height(24.dp)
        ) {
            val canvasWidth = size.width
            val totalRange = (maxValue - minValue + 1).toFloat()
            var rangeOffsetX = 0f

            rangeItemList.forEachIndexed { index, rangeItem ->
                val rangeLength = (rangeItem.maxValue - rangeItem.minValue + 1).toFloat()
                val rangeWidth = if (index == rangeItemList.lastIndex) {
                    canvasWidth - rangeOffsetX
                } else {
                    (canvasWidth * rangeLength / totalRange).roundToInt().toFloat()
                }

                val shouldDrawTick = index < rangeItemList.size - 1
                if (shouldDrawTick) {
                    val text = rangeItem.maxValue.toString()
                    val textLayoutResult = textMeasurer.measure(text)
                    val textSize = textLayoutResult.size

                    val tickX = rangeOffsetX + rangeWidth
                    val labelX = tickX - textSize.width / 2f

                    val lineOffset = Offset(x = tickX, y = 0f)
                    val labelOffset = Offset(x = labelX, y = markerLineHeight + markerLabelSpace)

                    drawLine(
                        color = gray,
                        start = lineOffset,
                        end = lineOffset.copy(y = markerLineHeight),
                        strokeWidth = density,
                    )

                    drawText(
                        textMeasurer = textMeasurer,
                        style = textStyle,
                        text = text,
                        topLeft = labelOffset,
                    )
                }

                rangeOffsetX += rangeWidth
            }
        }
    }
}

@Preview(widthDp = 170, heightDp = 170, backgroundColor = 0xFFFFFF, showBackground = true)
@Preview(widthDp = 170, heightDp = 170, locale = "zh", backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun Vo2MaxWidgetPreview() {
    M3AppTheme {
        Vo2MaxWidget(
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth(),
            widgetInfo = Vo2MaxWidgetInfo(
                latestVo2Max = 36f,
                latestVo2MaxDate = 1736245494239L,
                state = Vo2MaxState.GOOD,
                rangeItemList = listOf(
                    Vo2MaxRange(22, 26, Vo2MaxState.VERY_POOR, true),
                    Vo2MaxRange(27, 30, Vo2MaxState.POOR, true),
                    Vo2MaxRange(31, 35, Vo2MaxState.FAIR, true),
                    Vo2MaxRange(36, 41, Vo2MaxState.GOOD, true),
                    Vo2MaxRange(42, 46, Vo2MaxState.EXCELLENT, true),
                    Vo2MaxRange(47, 51, Vo2MaxState.SUPERIOR, true),
                )
            ),
            editMode = false,
            onClick = {},
            onLongClick = {},
            onRemoveClick = {}
        )
    }
}

@Preview(widthDp = 170, heightDp = 170, backgroundColor = 0xFFFFFF, showBackground = true)
@Preview(widthDp = 170, heightDp = 170, locale = "zh", backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun Vo2MaxWidgetEmptyPreview() {
    M3AppTheme {
        Vo2MaxWidget(
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth(),
            widgetInfo = Vo2MaxWidgetInfo(
                latestVo2Max = null,
                latestVo2MaxDate = 0L,
                state = Vo2MaxState.GOOD,
                rangeItemList = listOf(
                    Vo2MaxRange(22, 26, Vo2MaxState.VERY_POOR, true),
                    Vo2MaxRange(27, 30, Vo2MaxState.POOR, true),
                    Vo2MaxRange(31, 35, Vo2MaxState.FAIR, true),
                    Vo2MaxRange(36, 41, Vo2MaxState.GOOD, true),
                    Vo2MaxRange(42, 46, Vo2MaxState.EXCELLENT, true),
                    Vo2MaxRange(47, 51, Vo2MaxState.SUPERIOR, true),
                )
            ),
            editMode = false,
            onClick = {},
            onLongClick = {},
            onRemoveClick = {}
        )
    }
}
