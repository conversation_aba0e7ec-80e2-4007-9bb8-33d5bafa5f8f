package com.stt.android.home.dashboardv2.widgets.dataloader

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.domain.diary.Vo2MaxRange
import com.stt.android.domain.diary.Vo2MaxState
import com.stt.android.domain.diary.tss.GetLatestVo2MaxUseCase
import com.stt.android.home.dashboardv2.usecase.DashboardVo2MaxUseCase
import com.stt.android.home.dashboardv2.widgets.Vo2MaxWidgetInfo
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import javax.inject.Inject
import kotlin.collections.map
import kotlin.math.ceil
import kotlin.math.floor

internal class Vo2MaxWidgetDataLoader @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val getLatestVo2MaxUseCase: GetLatestVo2MaxUseCase,
    private val dashboardVo2MaxUseCase: DashboardVo2MaxUseCase,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : WidgetDataLoader<Vo2MaxWidgetInfo>() {
    override suspend fun load(param: Param): WidgetData<Vo2MaxWidgetInfo> =
        WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = workoutHeaderController.currentUserWorkoutUpdated
                .onStart { emit(Unit) }
                .map {
                    val fitnessExtension =
                        getLatestVo2MaxUseCase.getFitnessExtensionWithLatestVo2Max(
                            currentUserController.username
                        )
                    val workoutStartTimestamp = fitnessExtension?.workoutId?.let {
                        workoutHeaderController.findByIdsForCurrentUser(listOf(fitnessExtension.workoutId))
                            .first().startTime
                    }
                    val vo2Max = fitnessExtension?.vo2Max
                    val (state, items) = dashboardVo2MaxUseCase.getStateAndRangeItemList(vo2Max)
                    Vo2MaxWidgetInfo(
                        latestVo2Max = vo2Max,
                        latestVo2MaxDate = workoutStartTimestamp,
                        state = state,
                        rangeItemList = items.adjustByValue(vo2Max),
                    )
                }
                .flowOn(coroutinesDispatchers.io),
        )

    private fun List<Vo2MaxRange>.adjustByValue(vo2Max: Float?): List<Vo2MaxRange> {
        var minValue = first { it.state == Vo2MaxState.POOR }.minValue - VO2MAX_RANGE_OFFSET
        var maxValue = first { it.state == Vo2MaxState.EXCELLENT }.maxValue + VO2MAX_RANGE_OFFSET
        if (vo2Max != null && vo2Max < minValue) minValue = vo2Max
        if (vo2Max != null && vo2Max > maxValue) maxValue = vo2Max
        return map { range ->
            when (range.state) {
                Vo2MaxState.VERY_POOR -> range.copy(minValue = floor(minValue).toInt())
                Vo2MaxState.POOR,
                Vo2MaxState.FAIR,
                Vo2MaxState.GOOD,
                Vo2MaxState.EXCELLENT -> range

                Vo2MaxState.SUPERIOR -> range.copy(maxValue = ceil(maxValue).toInt())
            }
        }
    }

    companion object {
        private const val VO2MAX_RANGE_OFFSET = 5f
    }
}
