package com.stt.android.controllers

import com.google.gson.Gson
import com.squareup.moshi.Moshi
import com.stt.android.network.interfaces.ANetworkProvider
import com.stt.android.remote.otp.GenerateOTPUseCase
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class BackendController @Inject constructor(
    networkProvider: ANetworkProvider,
    gson: <PERSON>son,
    generateOTPUseCase: GenerateOTPUseCase,
    moshi: <PERSON><PERSON>,
) : BaseBackendController(networkProvider, gson, generateOTPUseCase, moshi)
