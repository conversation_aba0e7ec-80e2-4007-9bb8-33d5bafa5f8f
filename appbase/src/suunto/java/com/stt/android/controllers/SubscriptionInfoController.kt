package com.stt.android.controllers

import com.stt.android.domain.user.SubscriptionInfo
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SubscriptionInfoController @Inject constructor() : SubscriptionInfoControllerDataSource {
    override suspend fun fetchFromRemote(): List<SubscriptionInfo> = emptyList()

    override suspend fun loadFromLocal(): List<SubscriptionInfo> = emptyList()

    override suspend fun replaceLocalSubscriptionInfo(subscriptions: List<SubscriptionInfo>) {
        // Do nothing
    }
}
