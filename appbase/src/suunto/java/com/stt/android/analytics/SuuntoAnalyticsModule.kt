package com.stt.android.analytics

import com.suunto.connectivity.repository.AnalyticsRuntimeHook
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class SuuntoAnalyticsModule {

    @Binds
    abstract fun bindSuuntoAnalyticsRuntimeHook(suuntoAnalyticsRuntimeHook: SuuntoAnalyticsRuntimeHook): AnalyticsRuntimeHook
}
