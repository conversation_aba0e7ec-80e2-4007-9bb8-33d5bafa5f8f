package com.stt.android.analytics

data class WeatherAnalyticsData(
    val condition: String, // ClearSky/FewClouds/ScatteredClouds/BrokenClouds/ShowerRain/Rain/Thunderstorm/Snow/Mist/WorkoutNotOutdoors/DataNotAvailable (based on weather icon name)
    val temperature: String, // WorkoutNotOutdoors/DataNotAvailable/[Value]
    val windSpeed: String, // WorkoutNotOutdoors/DataNotAvailable/[Value]
    val windDirection: String, // WorkoutNotOutdoors/DataNotAvailable/[Value]
    val rainfallLastHour: String, // WorkoutNotOutdoors/DataNotAvailable/[Value]
    val snowfallLastHour: String, // WorkoutNotOutdoors/DataNotAvailable/[Value]
) {
    companion object {
        private const val WORKOUT_NOT_OUTDOORS = "WorkoutNotOutdoors"
        const val DATA_NOT_AVAILABLE = "DataNotAvailable"

        val NOT_OUTDOORS = WeatherAnalyticsData(
            WORKOUT_NOT_OUTDOORS,
            WORKOUT_NOT_OUTDOORS,
            WORKOUT_NOT_OUTDOORS,
            WORKOUT_NOT_OUTDOORS,
            WORKOUT_NOT_OUTDOORS,
            WORKOUT_NOT_OUTDOORS,
        )

        val NOT_AVAILABLE = WeatherAnalyticsData(
            DATA_NOT_AVAILABLE,
            DATA_NOT_AVAILABLE,
            DATA_NOT_AVAILABLE,
            DATA_NOT_AVAILABLE,
            DATA_NOT_AVAILABLE,
            DATA_NOT_AVAILABLE,
        )
    }
}
