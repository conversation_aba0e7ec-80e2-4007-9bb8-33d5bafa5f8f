package com.stt.android.analytics

import com.stt.android.analytics.AnalyticsEventProperty.ACTIVITY_TYPE
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_BOOTLOADER_VERSION
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_ALGORITHM
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_ALTITUDE
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_ASCENT_MODE
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_ASCENT_TIME
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_BATTERY_CHARGE_AT_END
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_BATTERY_CHARGE_AT_START
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_BATTERY_VOLTAGE_AT_END
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_BATTERY_VOLTAGE_AT_START
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_BOTTOM_MIXTURE_HELIUM
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_BOTTOM_MIXTURE_OXYGEN
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_BOTTOM_TIME
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_CONSERVATISM
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_DAYS_IN_SERIES
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_DEEPSTOP_ENABLED
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_DEPTH_AVG
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_DEPTH_MAX
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_DESATURATION_TIME
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_DIVE_MODE
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENTS_ERROR_COUNT
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENTS_GAS_SWITCH_COUNT
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_ALARM_ASCENT_SPEED
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_ALARM_CEILING_BROKEN
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_ALARM_DEEP_STOP_VIOLATION
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_ALARM_DILUENT_HYPEROXIA
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_ALARM_PO2_HIGH
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_ALARM_PO2_LOW
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_ALARM_SAFETY_STOP_BROKEN
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_ERROR_CEILING_BROKEN
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_NOTIFY_DECO_BROKEN_ACKNOWLEDGED
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_AIR_TIME
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_CCRO2_TANK_PRESSURE
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_CEILING_BROKEN
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_CNS100
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_CNS80
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_DEEP_STOP_BROKEN
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_DEEP_STOP_PENALTY
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_ICD
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_MANDATORY_SAFETY_STOP
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_MAX_DEPTH
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_MINI_LOCK
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_MISSED_DECO
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_OTU250
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_OTU300
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_PO2_HIGH
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_SAFETY_STOP_BROKEN
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_EVENT_COUNT_WARNING_TANK_PRESSURE
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_GAS_COUNT
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_GAS_WITH_TANKPOD_COUNT
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_LAST_DECO_STOP_DEPTH
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_MAXGF
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_MINGF
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_NUMBER_IN_SERIES
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_PREVIOUS_DIVE_DEPTH
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_SAFETYSTOP_TIME
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_SURFACE_PRESSURE
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_TISSUE_END_CNS
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_TISSUE_END_OLF
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_TISSUE_END_OTU
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_TISSUE_RESET
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_TISSUE_START_CNS
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_TISSUE_START_OLF
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_TISSUE_START_OTU
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_DIVE_VENTILATION_AVG
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_HARDWARE_VERSION
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_SOFTWARE_VERSION
import com.stt.android.analytics.AnalyticsEventProperty.SUUNTO_WATCH_SERIAL_NUMBER
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_DURATION
import com.stt.android.analytics.AnalyticsEventProperty.WORKOUT_PAUSE_DURATION
import kotlin.math.roundToInt

interface DiveEventAnalyticsData {
    val eventsErrorCount: Int?
    val eventsGasSwitchCount: Int?
    val eventsErrorCeilingBrokenCount: Int?
    val eventsAlarmSafetyStopBrokenCount: Int?
    val eventsAlarmAscentSpeedCount: Int?
    val eventsAlarmDiluentHyperoxiaCount: Int?
    val eventsAlarmDeepStopViolationCount: Int?
    val eventsAlarmCeilingBrokenCount: Int?
    val eventsAlarmPo2HighCount: Int?
    val eventsAlarmPo2LowCount: Int?
    val eventsWarningSafetyStopBrokenCount: Int?
    val eventsWarningDeepStopBrokenCount: Int?
    val eventsWarningCeilingBrokenCount: Int?
    val eventsWarningPo2HighCount: Int?
    val eventsWarningMissedDecoCount: Int?
    val eventsWarningMiniLockCount: Int?
    val eventsWarningICDCount: Int?
    val eventsWarningDeepStopPenaltyCount: Int?
    val eventsWarningMandatorySafetyStopCount: Int?
    val eventsWarningOTU250Count: Int?
    val eventsWarningOTU300Count: Int?
    val eventsWarningCNS80Count: Int?
    val eventsWarningCNS100Count: Int?
    val eventsWarningMaxDepthCount: Int?
    val eventsWarningAirTimeCount: Int?
    val eventsWarningTankPressureCount: Int?
    val eventsWarningCCRO2TankPressureCount: Int?
    val eventsNotifyDecoBrokenAcknowledgedCount: Int?
}

data class DiveAnalyticsData constructor(
    val activityType: String?,
    val watchSerialNumber: String?,
    val swVersion: String?,
    val hwVersion: String?,
    val bootloaderVersion: String?,
    val batteryChargeAtStart: Int?,
    val batteryVoltageAtStart: Int?,
    val batteryChargeAtEnd: Int?,
    val batteryVoltageAtEnd: Int?,
    val daysInSeries: Int?,
    val numberInSeries: Int?,
    val algorithm: String?,
    val surfacePressure: Int?,
    val minGF: Int?,
    val maxGF: Int?,
    val conservatism: Int?,
    val altitude: Int?,
    val deepstopEnabled: Boolean?,
    val safetystopTime: Int?,
    val lastDecoStopDepth: Float?, // 1 decimal
    val ascentMode: String?,
    val previousDiveDepth: Float?, // 2 decimals
    val tissueStartCNS: Float?, // 1 decimal
    val tissueStartOTU: Int?,
    val tissueStartOLF: Float?, // 1 decimal
    val tissueEndCNS: Float?, // 1 decimal
    val tissueEndOTU: Int?,
    val tissueEndOLF: Float?, // 1 decimal
    val diveMode: String?,
    val ascentTime: Int?,
    val bottomTime: Int?,
    val bottomMixtureOxygen: Int?,
    val bottomMixtureHelium: Int?,
    val desaturationTime: Int?,
    val gasCount: Int?,
    val gasWithTankpodCount: Int?,
    val duration: Int?,
    val pauseDuration: Int?,
    val depthMax: Float?, // 2 decimals
    val depthAvg: Float?, // 2 decimals
    val ventilationAvg: Float?, // 1 decimal
    val tissueReset: Boolean?,
    override val eventsErrorCount: Int?,
    override val eventsGasSwitchCount: Int?,
    override val eventsErrorCeilingBrokenCount: Int?,
    override val eventsAlarmSafetyStopBrokenCount: Int?,
    override val eventsAlarmAscentSpeedCount: Int?,
    override val eventsAlarmDiluentHyperoxiaCount: Int?,
    override val eventsAlarmDeepStopViolationCount: Int?,
    override val eventsAlarmCeilingBrokenCount: Int?,
    override val eventsAlarmPo2HighCount: Int?,
    override val eventsAlarmPo2LowCount: Int?,
    override val eventsWarningSafetyStopBrokenCount: Int?,
    override val eventsWarningDeepStopBrokenCount: Int?,
    override val eventsWarningCeilingBrokenCount: Int?,
    override val eventsWarningPo2HighCount: Int?,
    override val eventsWarningMissedDecoCount: Int?,
    override val eventsWarningMiniLockCount: Int?,
    override val eventsWarningICDCount: Int?,
    override val eventsWarningDeepStopPenaltyCount: Int?,
    override val eventsWarningMandatorySafetyStopCount: Int?,
    override val eventsWarningOTU250Count: Int?,
    override val eventsWarningOTU300Count: Int?,
    override val eventsWarningCNS80Count: Int?,
    override val eventsWarningCNS100Count: Int?,
    override val eventsWarningMaxDepthCount: Int?,
    override val eventsWarningAirTimeCount: Int?,
    override val eventsWarningTankPressureCount: Int?,
    override val eventsWarningCCRO2TankPressureCount: Int?,
    override val eventsNotifyDecoBrokenAcknowledgedCount: Int?

) : DiveEventAnalyticsData {

    fun toAnalyticsProperties(): AnalyticsProperties {
        return AnalyticsProperties().apply {
            watchSerialNumber?.let { put(SUUNTO_WATCH_SERIAL_NUMBER, it) }
            activityType?.let { put(ACTIVITY_TYPE, it) }
            swVersion?.let { put(SUUNTO_SOFTWARE_VERSION, it) }
            hwVersion?.let { put(SUUNTO_HARDWARE_VERSION, it) }
            bootloaderVersion?.let { put(SUUNTO_BOOTLOADER_VERSION, it) }
            batteryChargeAtStart?.let { put(SUUNTO_DIVE_BATTERY_CHARGE_AT_START, it) }
            batteryVoltageAtStart?.let { put(SUUNTO_DIVE_BATTERY_VOLTAGE_AT_START, it) }
            batteryChargeAtEnd?.let { put(SUUNTO_DIVE_BATTERY_CHARGE_AT_END, it) }
            batteryVoltageAtEnd?.let { put(SUUNTO_DIVE_BATTERY_VOLTAGE_AT_END, it) }
            daysInSeries?.let { put(SUUNTO_DIVE_DAYS_IN_SERIES, it) }
            numberInSeries?.let { put(SUUNTO_DIVE_NUMBER_IN_SERIES, it) }
            algorithm?.let { put(SUUNTO_DIVE_ALGORITHM, it) }
            surfacePressure?.let { put(SUUNTO_DIVE_SURFACE_PRESSURE, it) }
            minGF?.let { put(SUUNTO_DIVE_MINGF, it) }
            maxGF?.let { put(SUUNTO_DIVE_MAXGF, it) }
            conservatism?.let { put(SUUNTO_DIVE_CONSERVATISM, it) }
            altitude?.let { put(SUUNTO_DIVE_ALTITUDE, it) }
            deepstopEnabled?.let { put(SUUNTO_DIVE_DEEPSTOP_ENABLED, it) }
            safetystopTime?.let { put(SUUNTO_DIVE_SAFETYSTOP_TIME, it) }
            lastDecoStopDepth?.let { put(SUUNTO_DIVE_LAST_DECO_STOP_DEPTH, it) }
            ascentMode?.let { put(SUUNTO_DIVE_ASCENT_MODE, it) }
            previousDiveDepth?.let { put(SUUNTO_DIVE_PREVIOUS_DIVE_DEPTH, it) }
            tissueStartCNS?.let { put(SUUNTO_DIVE_TISSUE_START_CNS, it) }
            tissueStartOTU?.let { put(SUUNTO_DIVE_TISSUE_START_OTU, it) }
            tissueStartOLF?.let { put(SUUNTO_DIVE_TISSUE_START_OLF, it) }
            tissueEndCNS?.let { put(SUUNTO_DIVE_TISSUE_END_CNS, it) }
            tissueEndOTU?.let { put(SUUNTO_DIVE_TISSUE_END_OTU, it) }
            tissueEndOLF?.let { put(SUUNTO_DIVE_TISSUE_END_OLF, it) }
            diveMode?.let { put(SUUNTO_DIVE_DIVE_MODE, it) }
            ascentTime?.let { put(SUUNTO_DIVE_ASCENT_TIME, it) }
            bottomTime?.let { put(SUUNTO_DIVE_BOTTOM_TIME, it) }
            bottomMixtureOxygen?.let { put(SUUNTO_DIVE_BOTTOM_MIXTURE_OXYGEN, it) }
            bottomMixtureHelium?.let { put(SUUNTO_DIVE_BOTTOM_MIXTURE_HELIUM, it) }
            desaturationTime?.let { put(SUUNTO_DIVE_DESATURATION_TIME, it) }
            gasCount?.let { put(SUUNTO_DIVE_GAS_COUNT, it) }
            gasWithTankpodCount?.let { put(SUUNTO_DIVE_GAS_WITH_TANKPOD_COUNT, it) }
            duration?.let { put(WORKOUT_DURATION, it) }
            pauseDuration?.let { put(WORKOUT_PAUSE_DURATION, it) }
            depthMax?.let { put(SUUNTO_DIVE_DEPTH_MAX, it) }
            depthAvg?.let { put(SUUNTO_DIVE_DEPTH_AVG, it) }
            ventilationAvg?.let { put(SUUNTO_DIVE_VENTILATION_AVG, it) }
            tissueReset?.let { putYesNo(SUUNTO_DIVE_TISSUE_RESET, it) }
            eventsErrorCount?.let { put(SUUNTO_DIVE_EVENTS_ERROR_COUNT, it) }
            eventsGasSwitchCount?.let { put(SUUNTO_DIVE_EVENTS_GAS_SWITCH_COUNT, it) }
            eventsErrorCeilingBrokenCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_ERROR_CEILING_BROKEN, it) }
            eventsAlarmSafetyStopBrokenCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_ALARM_SAFETY_STOP_BROKEN, it) }
            eventsAlarmAscentSpeedCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_ALARM_ASCENT_SPEED, it) }
            eventsAlarmDiluentHyperoxiaCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_ALARM_DILUENT_HYPEROXIA, it) }
            eventsAlarmDeepStopViolationCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_ALARM_DEEP_STOP_VIOLATION, it) }
            eventsAlarmCeilingBrokenCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_ALARM_CEILING_BROKEN, it) }
            eventsAlarmPo2HighCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_ALARM_PO2_HIGH, it) }
            eventsAlarmPo2LowCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_ALARM_PO2_LOW, it) }
            eventsWarningSafetyStopBrokenCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_SAFETY_STOP_BROKEN, it) }
            eventsWarningDeepStopBrokenCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_DEEP_STOP_BROKEN, it) }
            eventsWarningCeilingBrokenCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_CEILING_BROKEN, it) }
            eventsWarningPo2HighCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_PO2_HIGH, it) }
            eventsWarningMissedDecoCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_MISSED_DECO, it) }
            eventsWarningMiniLockCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_MINI_LOCK, it) }
            eventsWarningICDCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_ICD, it) }
            eventsWarningDeepStopPenaltyCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_DEEP_STOP_PENALTY, it) }
            eventsWarningMandatorySafetyStopCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_MANDATORY_SAFETY_STOP, it) }
            eventsWarningOTU250Count?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_OTU250, it) }
            eventsWarningOTU300Count?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_OTU300, it) }
            eventsWarningCNS80Count?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_CNS80, it) }
            eventsWarningCNS100Count?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_CNS100, it) }
            eventsWarningMaxDepthCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_MAX_DEPTH, it) }
            eventsWarningAirTimeCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_AIR_TIME, it) }
            eventsWarningTankPressureCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_TANK_PRESSURE, it) }
            eventsWarningCCRO2TankPressureCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_WARNING_CCRO2_TANK_PRESSURE, it) }
            eventsNotifyDecoBrokenAcknowledgedCount?.let { put(SUUNTO_DIVE_EVENT_COUNT_NOTIFY_DECO_BROKEN_ACKNOWLEDGED, it) }
        }
    }

    companion object {

        private val batteryRegex = Regex("^Charge: (\\d*\\.?\\d*)%, Voltage: (\\d*\\.?\\d*)V\$")

        /**
         * Extracts battery charge value from string "Charge: 87%, Voltage: 4.054V"
         * @return charge
         */
        @JvmStatic
        fun batteryValueFromJsonEntry(jsonEntry: String?): Int? {
            jsonEntry ?: return null
            return batteryRegex.matchEntire(jsonEntry)?.groups?.get(1)
                ?.value?.toFloatOrNull()?.roundToInt()
        }

        /**
         * Extracts voltage charge value from string "Charge: 87%, Voltage: 4.054V"
         * @return voltage value in mV
         */
        @JvmStatic
        fun voltageValueFromJsonEntry(jsonEntry: String?): Int? {
            jsonEntry ?: return null
            return batteryRegex.matchEntire(jsonEntry)?.groups?.get(2)
                ?.value?.toFloatOrNull()?.times(1000)?.roundToInt()
        }
    }
}
