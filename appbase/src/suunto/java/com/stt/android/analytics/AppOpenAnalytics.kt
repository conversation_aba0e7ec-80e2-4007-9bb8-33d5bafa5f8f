package com.stt.android.analytics

import android.content.SharedPreferences
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.eventtracking.EventTracker
import com.stt.android.logs.VisibleActivityTracker
import com.stt.android.watch.SuuntoWatchModel
import javax.inject.Inject

class AppOpenAnalytics @Inject constructor(
    currentUserController: CurrentUserController,
    eventTracker: EventTracker,
    sharedPreferences: SharedPreferences,
    visibleActivityTracker: VisibleActivityTracker,
    dispatchers: CoroutinesDispatchers,
    private val suuntoWatchModel: SuuntoWatchModel,
) : BaseAppOpenAnalytics(
    currentUserController,
    eventTracker,
    sharedPreferences,
    visibleActivityTracker,
    dispatchers,
) {
    override suspend fun fetchWatchName() = runSuspendCatching {
        suuntoWatchModel.currentWatch().suuntoBtDevice.deviceType.displayName
    }.getOrNull()
}
