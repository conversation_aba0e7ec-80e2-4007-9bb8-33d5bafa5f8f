package com.stt.android.easterEgg

import android.app.Application
import android.content.Context
import android.os.Environment
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.logging.TimberInMemoryTree
import com.stt.android.refreshable.Refreshables
import com.suunto.connectivity.ScLib
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Easter egg implementation for Suunto application.
 */
@Singleton
class EasterEgg @Inject
constructor(
    private val application: Application,
    private val scLib: ScLib,
    timberInMemoryTree: TimberInMemoryTree,
    refreshables: Refreshables,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : EasterEggBase(timberInMemoryTree, refreshables, coroutinesDispatchers) {
    private val coroutineScope = CoroutineScope(SupervisorJob())

    init {
        coroutineScope.launch { setFlavorSpecificFileLogging(application, false) }
    }

    override suspend fun setFlavorSpecificFileLogging(context: Context, logToFile: Boolean): Unit =
        withContext(coroutinesDispatchers.io) {
            runSuspendCatching {
                if (logToFile) {
                    val dst = getConnectivityDumpFolder(context) ?: return@runSuspendCatching
                    scLib.startLoggingToFile(dst)
                        .await()
                } else {
                    scLib.stopLoggingToFile()
                        .toCompletable()
                        .await()
                }
            }.onFailure { e ->
                Timber.w(e, "Error in setFlavorSpecificFileLogging")
            }
        }

    private fun getConnectivityDumpFolder(context: Context): File? {
        if (Environment.getExternalStorageState() != Environment.MEDIA_MOUNTED) {
            Timber.w("Unable to dump internal state. External storage is not ready")
            return null
        }
        val dst = File(context.getExternalFilesDir(null), CONNECTIVITY_DUMP_FOLDER_NAME)
        if (!dst.isDirectory) {
            if (!dst.mkdirs()) {
                Timber.w("Unable to create destination folder %s", dst.toString())
                return null
            }
        }
        return dst
    }

    companion object {

        /**
         * Folder in the sdcard where the connectivity log files will be saved
         */
        private const val CONNECTIVITY_DUMP_FOLDER_NAME =
            "$DUMP_FOLDER_NAME/suunto-connectivity-dump"
    }
}
