package com.stt.android.workouts.sharepreview

import com.stt.android.remote.MediaTypes
import com.stt.android.remote.sharedimagereview.SharedImageReviewRestApi
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import javax.inject.Inject

class SharedImageReviewDataSource @Inject constructor(
    private val sharedImageReviewRestApi: SharedImageReviewRestApi
) {

    /**
     *  true: review Passed
     *  false: image is illegal
     */
    suspend fun getImageReviewResult(imageFile: File): Boolean {
        return sharedImageReviewRestApi.reviewSharedImage(imageFile.asRequestBody(MediaTypes.OCTET_STREAM_CONTENT_TYPE))
            .payloadOrThrow() == 1
    }
}
