package com.stt.android.workouts.sharepreview

import com.squareup.moshi.Moshi
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import com.stt.android.remote.sharedimagereview.SharedImageReviewRestApi
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient

@Module
@InstallIn(SingletonComponent::class)
abstract class BrandWorkoutSharePreviewModule {

    companion object {
        @Provides
        fun provideSharedImageReviewRestAPI(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: <PERSON>shi
        ): SharedImageReviewRestApi {
            return RestApiFactory.buildRestApi(
                sharedClient,
                baseUrl,
                SharedImageReviewRestApi::class.java,
                BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }
    }
}
