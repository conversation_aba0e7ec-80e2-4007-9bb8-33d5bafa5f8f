package com.stt.android.watch.preference.notification

import android.app.Activity
import android.os.Bundle
import android.view.MenuItem
import androidx.appcompat.app.AppCompatActivity
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.commit
import com.stt.android.R
import com.stt.android.databinding.ActivityWatchAppNotificationsPermissionsBinding
import com.stt.android.watch.preference.SetupPreference
import com.stt.android.watch.preference.SetupPreferenceActivity
import com.stt.android.watch.preference.notification.SetupPreferenceAppNotificationsViewModel.Companion.KEY_SETUP_PREFERENCE
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SetupPreferenceAppNotificationsActivity : AppCompatActivity() {

    lateinit var binding: ActivityWatchAppNotificationsPermissionsBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DataBindingUtil.setContentView(
            this,
            R.layout.activity_watch_app_notifications_permissions
        )
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayShowHomeEnabled(false)
            setDisplayHomeAsUpEnabled(true)
        }
        intent.getParcelableExtra<SetupPreference>(SetupPreferenceActivity.EXTRA_SETUP_PREFERENCE)
            ?.let {
                val fragment = supportFragmentManager.fragmentFactory.instantiate(
                    classLoader,
                    SetupPreferenceAppNotificationsFragment::class.java.name
                ) as SetupPreferenceAppNotificationsFragment
                fragment.arguments = Bundle().apply {
                    putParcelable(KEY_SETUP_PREFERENCE, it)
                }

                supportFragmentManager.commit {
                    replace(R.id.appNotifications, fragment)
                }
            }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                onBackPressed()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun finish() {
        supportFragmentManager.fragments
            .firstOrNull { it is SetupPreferenceAppNotificationsFragment }
            ?.let { it as SetupPreferenceAppNotificationsFragment }
            ?.viewModel
            ?.setupPreference
            ?.let {
                setResult(Activity.RESULT_OK, intent.apply {
                    putExtra(SetupPreferenceActivity.EXTRA_SETUP_PREFERENCE, it)
                })
            }
        super.finish()
    }
}
