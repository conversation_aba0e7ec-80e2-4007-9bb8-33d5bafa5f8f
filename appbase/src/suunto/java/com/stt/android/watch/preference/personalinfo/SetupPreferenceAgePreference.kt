package com.stt.android.watch.preference.personalinfo

import android.content.Context
import android.util.AttributeSet
import com.stt.android.STTApplication
import com.stt.android.home.settings.BaseAgePreference

class SetupPreferenceAgePreference @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0,
) : BaseAgePreference(context, attrs, defStyleAttr, defStyleRes) {

    override fun onAttached() {
        super.onAttached()
        STTApplication.getComponent().inject(this)
    }
}
