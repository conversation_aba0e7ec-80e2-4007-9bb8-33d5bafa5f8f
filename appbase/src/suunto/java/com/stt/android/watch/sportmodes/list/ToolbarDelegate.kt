package com.stt.android.watch.sportmodes.list

import androidx.lifecycle.LiveData
import com.stt.android.ui.utils.SingleLiveEvent
import dagger.hilt.android.scopes.ActivityRetainedScoped
import javax.inject.Inject

/**
 * this interface is implemented by activity class and injected to the view model of the fragment [SportModesListViewModel]
 * that holds the items that need to start action mode such as [SportModeItem]
 */
@ActivityRetainedScoped
class ToolbarDelegate
@Inject constructor() {
    private val _actionModeEvent = SingleLiveEvent<ActionModeEvent>()

    val actionModeEvent: LiveData<ActionModeEvent>
        get() = _actionModeEvent

    fun changeToolbarToPickMode() {
        _actionModeEvent.value = ActionModeEvent.ChangeToolbarToPickMode
    }

    fun changeToolbarToDefaultMode() {
        _actionModeEvent.value = ActionModeEvent.ChangeToolbarToDefaultMode
    }
}
