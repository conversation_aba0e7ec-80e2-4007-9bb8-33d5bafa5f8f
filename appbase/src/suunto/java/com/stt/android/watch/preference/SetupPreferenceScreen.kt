package com.stt.android.watch.preference

import android.annotation.SuppressLint
import android.view.View
import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.core.view.ViewCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentContainerView
import androidx.fragment.app.commit
import com.stt.android.R
import com.stt.android.compose.component.HorizontalPagerIndicator
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.spacing
import kotlinx.coroutines.launch

@Composable
fun SetupPreferenceScreen(
    titles: List<Int>,
    fragments: List<Fragment>,
    onBackPressed: () -> Unit,
    onFinish: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    if (context !is FragmentActivity) return
    val fragmentsProviders = remember {
        fragments.map {
            { it }
        }
    }
    val views = remember {
        fragments.map {
            FragmentContainerView(context).apply {
                id = ViewCompat.generateViewId()
            }
        }
    }

    Scaffold(
        modifier = modifier,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = stringResource(R.string.setup_preference_title).uppercase(),
                    )
                },
                navigationIcon = {
                    SuuntoIconButton(
                        icon = SuuntoIcons.ActionClose,
                        onClick = onBackPressed,
                        contentDescription = stringResource(R.string.back),
                    )
                },
                backgroundColor = Color.White,
            )
        },
    ) { paddingValues ->
        Content(
            fragmentProviders = fragmentsProviders,
            titles = titles,
            views = views,
            onFinish = onFinish,
            modifier = Modifier
                .narrowContent()
                .background(Color.White)
                .padding(paddingValues)
        )
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun Content(
    fragmentProviders: List<() -> Fragment>,
    modifier: Modifier = Modifier,
    titles: List<Int> = emptyList(),
    views: List<View> = emptyList(),
    onFinish: () -> Unit = {},
) {
    val coroutineScope = rememberCoroutineScope()
    val pageCount = fragmentProviders.size
    val pagerState = rememberPagerState {
        pageCount
    }

    fun prevPage() = coroutineScope.launch {
        pagerState.animateScrollToPage(pagerState.currentPage - 1)
    }

    fun nextPage() = coroutineScope.launch {
        pagerState.animateScrollToPage(pagerState.currentPage + 1)
    }

    ConstraintLayout(modifier = modifier.fillMaxSize()) {
        val (subTitle, pager, button, indicator, left, right) = createRefs()

        SubTitle(
            titleRes = titles.getOrNull(pagerState.currentPage) ?: 0,
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(subTitle) {
                    top.linkTo(parent.top)
                }
        )

        HorizontalPager(
            state = pagerState,
            beyondViewportPageCount = 1,
            modifier = Modifier
                .constrainAs(pager) {
                    top.linkTo(subTitle.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(button.top)
                    height = Dimension.fillToConstraints
                }
        ) { page ->
            FragmentContainerPage(
                fragmentProvider = fragmentProviders[page],
                viewProvider = { views.getOrNull(page) })
        }

        val buttonColors = ButtonDefaults.buttonColors(
            backgroundColor = colorResource(id = R.color.suunto_blue),
            contentColor = Color.White // Text color
        )
        val atFirst = pagerState.currentPage == 0
        val atLastPage = pagerState.currentPage == pageCount - 1

        Button(
            modifier = Modifier
                .padding(
                    horizontal = MaterialTheme.spacing.large,
                    vertical = MaterialTheme.spacing.smaller,
                )
                .fillMaxWidth()
                .height(MaterialTheme.spacing.xxlarge)
                .constrainAs(button) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(indicator.top)
                },
            shape = RoundedCornerShape(MaterialTheme.spacing.small),
            colors = buttonColors,
            onClick = {
                if (atLastPage) {
                    onFinish()
                } else {
                    nextPage()
                }
            }
        ) {
            Text(
                text = if (atLastPage) {
                    stringResource(id = R.string.done).uppercase()
                } else {
                    stringResource(id = R.string.setup_preference_bottom_button_next).uppercase()
                }
            )
        }

        HorizontalPagerIndicator(
            pagerState = pagerState,
            modifier = Modifier
                .padding(MaterialTheme.spacing.smaller)
                .constrainAs(indicator) {
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom)
                }
        )

        if (!atFirst) {
            SuuntoIconButton(
                icon = SuuntoIcons.ActionBack,
                onClick = ::prevPage,
                modifier = Modifier
                    .padding(start = MaterialTheme.spacing.small)
                    .constrainAs(left) {
                        top.linkTo(indicator.top)
                        bottom.linkTo(indicator.bottom)
                        start.linkTo(parent.start)
                    },
            )
        }

        if (!atLastPage) {
            IconButton(
                modifier = Modifier
                    .padding(end = MaterialTheme.spacing.small)
                    .constrainAs(right) {
                        top.linkTo(indicator.top)
                        bottom.linkTo(indicator.bottom)
                        end.linkTo(parent.end)
                    },
                onClick = ::nextPage
            ) {
                Icon(
                    painter = painterResource(R.drawable.all_ic_arrow_right),
                    contentDescription = "right"
                )
            }
        }
    }
}

@Composable
private fun SubTitle(modifier: Modifier = Modifier, titleRes: Int = 0) {
    Column(modifier = modifier) {
        if (titleRes != 0) {
            TopAppBar(
                title = {
                    Text(
                        text = stringResource(titleRes).uppercase(),
                    )
                },
                backgroundColor = Color.White,
                elevation = 0.dp
            )
            Divider()
        }
    }
}

@SuppressLint("DetachAndAttachSameFragment")
@Composable
private fun FragmentContainerPage(
    fragmentProvider: () -> Fragment,
    viewProvider: () -> View?,
    modifier: Modifier = Modifier,
) {
    val fragmentManager = (LocalActivity.current as? FragmentActivity)?.supportFragmentManager
    val view = viewProvider()
    if (fragmentManager == null || view == null) {
        Box(
            modifier = modifier
                .background(Color.Transparent)
                .fillMaxSize(),
            contentAlignment = Alignment.Center,
        ) {
            Text(text = "Test text")
        }
    } else {
        AndroidView(
            modifier = modifier.fillMaxSize(),
            factory = {
                val fragment = fragmentProvider()
                fragmentManager.commit {
                    if (!fragment.isAdded) {
                        add(view.id, fragment)
                    }
                }
                view
            },
        )
    }
}

@Preview
@Composable
private fun SetupPreferenceScreenPreview() {
    Content(
        fragmentProviders = listOf(
            { DummyFragment.instance("test") },
            { DummyFragment.instance("test") },
        )
    )
}

@Preview(widthDp = 1080, showBackground = true)
@Composable
private fun SetupPreferenceScreenPreview1() {
    Content(
        fragmentProviders = listOf(
            { DummyFragment.instance("test") },
            { DummyFragment.instance("test") },
        ),
        modifier = Modifier.narrowContent()
    )
}

