package com.stt.android.watch.sportmodes.create.multisport.composable

import androidx.compose.foundation.layout.Column
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.domain.workout.ActivityType
import com.stt.android.watch.sportmodes.create.multisport.MultisportModeEditViewState

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun MultisportModeEditTopBar(
    state: MultisportModeEditViewState,
    onBackClick: () -> Unit,
    onSaveClick: () -> Unit,
    modifier: Modifier = Modifier,
    unableToEditReason: Int? = null,
) {
    val systemUiController = rememberSystemUiController()
    val isSelection = state !is MultisportModeEditViewState.Confirm
    val containerColor =
        if (isSelection) MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.surface
    val contentColor =
        if (isSelection) MaterialTheme.colorScheme.surface else MaterialTheme.colorScheme.onSurface
    LaunchedEffect(state.javaClass) {
        systemUiController.setStatusBarColor(
            color = containerColor,
            darkIcons = !isSelection
        )
        systemUiController.setNavigationBarColor(
            color = containerColor,
            darkIcons = !isSelection
        )
    }

    val title = when (state) {
        is MultisportModeEditViewState.SelectParent -> stringResource(R.string.select_sport)
        is MultisportModeEditViewState.SelectChildren -> {
            if (state.selectedChildren.any { !it.isTransition }) {
                stringResource(
                    R.string.sport_mode_selection_text,
                    state.selectedChildren.count { !it.isTransition },
                )
            } else {
                stringResource(R.string.select_sport_mode)
            }
        }

        is MultisportModeEditViewState.Confirm -> stringResource(state.parentActivityType.localizedStringId)
    }

    val canSave = state !is MultisportModeEditViewState.SelectParent

    val saveEnabled = when (state) {
        is MultisportModeEditViewState.SelectParent -> false
        is MultisportModeEditViewState.SelectChildren -> state.reachedMinSelectedCount
        is MultisportModeEditViewState.Confirm -> state.reachedMinSelectedCount && state.nameErrorTips == 0
    } && unableToEditReason == null

    TopAppBar(
        title = { Text(title.uppercase()) },
        navigationIcon = {
            SuuntoIconButton(
                icon = if (isSelection) {
                    SuuntoIcons.ActionClose
                } else {
                    SuuntoIcons.ActionBack
                },
                onClick = onBackClick,
                contentDescription = stringResource(R.string.back),
            )
        },
        actions = {
            if (canSave) {
                IconButton(
                    enabled = saveEnabled,
                    onClick = onSaveClick,
                ) {
                    Icon(
                        painter = painterResource(R.drawable.checkmark_grey),
                        tint = contentColor.copy(
                            alpha = if (saveEnabled) 1f else 0.5f
                        ),
                        contentDescription = null,
                    )
                }
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = containerColor,
            navigationIconContentColor = contentColor,
            titleContentColor = contentColor,
            actionIconContentColor = contentColor,
        ),
        modifier = modifier,
    )
}

@Preview
@Composable
private fun MultisportModeEditTopBarPreview(
    @PreviewParameter(MultisportModeEditTopBarPreviewProvider::class)
    state: MultisportModeEditViewState,
) {
    M3AppTheme {
        Column {
            MultisportModeEditTopBar(
                state = state,
                onBackClick = {},
                onSaveClick = {},
            )
        }
    }
}

class MultisportModeEditTopBarPreviewProvider :
    PreviewParameterProvider<MultisportModeEditViewState> {
    override val values: Sequence<MultisportModeEditViewState> = sequenceOf(
        MultisportModeEditViewState.SelectParent(
            multisportActivityTypes = listOf(
                ActivityType.MULTISPORT,
                ActivityType.RUNNING,
                ActivityType.CYCLING,
            ),
            loading = false,
        ),
        MultisportModeEditViewState.SelectChildren(
            parentActivityType = ActivityType.MULTISPORT,
            children = previewChildren,
            selectedChildren = emptyList(),
            loading = false,
        ),
        MultisportModeEditViewState.Confirm(
            parentActivityType = ActivityType.MULTISPORT,
            selectedChildren = previewChildren.take(2),
            name = "aad",
            enableTransitions = true,
            loading = true,
            editing = true,
            nameErrorTips = R.string.sport_mode_name_short_error,
        ),
    )
}
