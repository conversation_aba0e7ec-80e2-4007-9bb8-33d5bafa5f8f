package com.stt.android.watch.sportmodes.create.multisport

import com.stt.android.domain.sportmodes.FetchSportModesUseCase
import com.stt.android.domain.sportmodes.SaveSportModesUseCase
import com.stt.android.domain.sportmodes.SportModeHeader
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.getMcIdForStId
import com.stt.android.watch.sportmodes.mappers.SportModeJsonEditor
import kotlinx.coroutines.rx2.await
import timber.log.Timber
import javax.inject.Inject

class SaveMultisportModeUseCase @Inject constructor(
    private val sportModesUseCase: FetchSportModesUseCase,
    private val saveSportModesUseCase: SaveSportModesUseCase,
    private val fetchSportModesUseCase: FetchSportModesUseCase,
    private val sportModeJsonEditor: SportModeJsonEditor,
) {
    suspend operator fun invoke(
        parentActivityType: ActivityType,
        multisportChildren: List<MultisportChild>,
        name: String,
        existingSportMode: SportModeHeader? = null,
        watchSportModes: String?,
    ) {
        val transitionMcId = sportModesUseCase.getTransitionId()
        val childActivityIds = multisportChildren.map { it.mcId ?: transitionMcId }

        val parentMcId = getMcIdForStId(parentActivityType.id)
        var modeTemplate = sportModesUseCase.fetchMultisportModeTemplate(
            parentMcId,
            childActivityIds,
            watchSportModes
        )
        if (existingSportMode != null) {
            modeTemplate = modeTemplate.copy(
                group = modeTemplate.group.copy(
                    id = existingSportMode.id
                )
            )
            Timber.d("parent activity: ${parentActivityType.id}, existing group id: ${existingSportMode.id}")
        }
        modeTemplate.group.id ?: run {
            Timber.w("Failed to create multisport mode, group id is null")
            return
        }

        val modes = modeTemplate.modes.mapIndexed { index, mode ->
            val multisportChild = multisportChildren[index]
            val existingChildModeId = multisportChild.modeId
            if (existingChildModeId != null) { // Update modes by reusing existing sport modes
                if (multisportChild.settings != null && multisportChild.displays != null) {
                    // Reuse existing child sport mode wrapped in multisport mode
                    Timber.d("Use existing sport mode $existingChildModeId")
                    mode.copy(
                        id = existingChildModeId,
                        settings = multisportChild.settings!!,
                        displays = multisportChild.displays!!
                    )
                } else {
                    // Reuse existing single sport mode in short list
                    Timber.d("Use existing single sport mode $existingChildModeId")
                    val settings =
                        fetchSportModesUseCase.fetchSportModeSettingsJson(existingChildModeId)
                            .await()
                    val displays =
                        fetchSportModesUseCase.fetchSportModeDisplaysJson(existingChildModeId)
                            .await()
                    mode.copy(id = existingChildModeId, settings = settings, displays = displays)
                }
            } else {
                mode
            }
        }

        val isNewMode = existingSportMode == null
        val modesUpdated = multisportChildren.any { it.settings == null || it.displays == null }
        saveSportModesUseCase.saveSportMode(
            group = sportModeJsonEditor.getGroupWithUpdatedName(name, modeTemplate.group)
                .let { group ->
                    sportModeJsonEditor.getGroupWithExistingGroupIds(group, modes.map { it.id })
                        .also { result ->
                            Timber.d("save multisport mode group: ${result.id}")
                        }
                },
            modes = modes.toTypedArray(),
            displayChanged = isNewMode || modesUpdated,
            settingsChanged = isNewMode || modesUpdated,
            groupChanged = true,
        ).await()
        Timber.d("parent activity: ${parentActivityType.id} saved")
    }
}
