package com.stt.android.watch.watchcontrol

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.await
import com.stt.android.utils.RxUtils.DEFAULT_TIMEOUT_SECONDS
import com.stt.android.utils.applyTimeout
import com.stt.android.utils.toV2Flowable
import com.stt.android.watch.SuuntoWatchModel
import com.suunto.connectivity.watch.WatchState
import com.suunto.connectivity.watchcontrol.WatchControlConsumer
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.withContext
import rx.Single
import javax.inject.Inject

interface WatchControlApi {
    fun supportsFindWatch(): Flow<Boolean>
    suspend fun startFindWatch(start: Boolean)
    fun subscribeFindWatch(): Flow<Boolean>
}

class WatchControlApiImpl @Inject constructor(
    private val suuntoWatchModel: SuuntoWatchModel,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : WatchControlApi {
    override fun supportsFindWatch(): Flow<Boolean> {
        return suuntoWatchModel
            .watchStates()
            .map { it.connectionState == WatchState.ConnectionState.CONNECTED }
            .distinctUntilChanged()
            .map { it && suuntoWatchModel.supportsRunOta2(false) }
    }

    override suspend fun startFindWatch(start: Boolean) {
        invokeApi { consumer, macAddress ->
            consumer.startFindWatch(macAddress, start)
        }
    }

    override fun subscribeFindWatch(): Flow<Boolean> {
        return suuntoWatchModel.currentWatch.flatMapObservable {
            it.suuntoRepositoryClient.watchControlConsumer.subscribeFindWatch(it.suuntoBtDevice.macAddress)
        }.toV2Flowable().asFlow()
    }

    private suspend fun getMacAddressAndConsumer(): Pair<String, WatchControlConsumer> {
        return suuntoWatchModel.currentWatch.await().let {
            val macAddress = it.suuntoBtDevice.macAddress
            val consumer = it.suuntoRepositoryClient.watchControlConsumer
            macAddress to consumer
        }
    }

    private suspend inline fun <reified T> invokeApi(
        timeoutSeconds: Long = DEFAULT_TIMEOUT_SECONDS,
        crossinline apiBlock: (consumer: WatchControlConsumer, macAddress: String) -> Single<T>
    ): T {
        return withContext(coroutinesDispatchers.io) {
            val (macAddress, consumer) = getMacAddressAndConsumer()
            apiBlock(consumer, macAddress)
                .applyTimeout(timeoutSeconds)
                .await()
        }
    }
}
