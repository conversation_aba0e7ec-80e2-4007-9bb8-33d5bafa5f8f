package com.stt.android.watch.sportmodes.list

import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.sportmodes.DeleteSportModesUseCase
import com.stt.android.domain.sportmodes.FetchSportModesUseCase
import com.stt.android.domain.sportmodes.SportModeHeader
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.getStIdForMcId
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.watch.sportmodes.SportModeViewModel
import com.xwray.groupie.Section
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Flowable
import io.reactivex.Scheduler
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import timber.log.Timber
import java.util.Collections
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class SportModesListViewModel
@Inject constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    private val fetchSportModesUseCase: FetchSportModesUseCase,
    private val deleteSportModesUseCase: DeleteSportModesUseCase,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : SportModeViewModel(ioThread, mainThread),
    OnSportModeSelectedListener,
    DeleteInProgressDelegate {

    private lateinit var sportModeHeaderItem: SportModeHeaderItem
    private var sportModeItemsList = Collections.synchronizedList(mutableListOf<SportModeItem>())
    private var isDeleteInProgress = false
    private var isWatchBusy = false

    val showDeleteAllAlertEvent = SingleLiveEvent<Any>()
    val multisportModeEditRequested = SingleLiveEvent<MultisportModeEditRequest>()
    val cannotDeleteMultisportChildEvent = SingleLiveEvent<String>()

    private var _currentSportModesJson: String = ""

    init {
        checkMultisportCustomizationSupported()
    }

    override fun isDeletionInProcess(): Boolean {
        return isDeleteInProgress
    }

    @MainThread
    private fun updateList(newList: List<SportModeItem>, notifyLoaded: Boolean) {
        sportModeItemsList.clear()
        sportModeItemsList.addAll(newList)
        if (notifyLoaded && !isDeleteInProgress) {
            notifyDataLoaded(listOf(Section(sportModeHeaderItem, newList)))
        }
    }

    override fun deleteSportMode(sportModeItem: SportModeItem) {
        if (sportModeItemsList.size == 1) {
            showDeleteAllAlertEvent.call()
            changeInDeletion(false)
            return
        }
        val inUsingMultisportModeNames = getInUsingMultisportModeNames(sportModeItem.sportModeHeader)
        if (inUsingMultisportModeNames.isNotEmpty()) {
            cannotDeleteMultisportChildEvent.postValue(inUsingMultisportModeNames)
            return
        }
        isDeleteInProgress = true
        sportModeHeaderItem.updateHeaderState(true, isListFull(), isListEmpty())
        notifyChangesInProgress()
        disposables.add(
            Flowable.fromIterable(listOf(sportModeItem))
                .concatMap {
                    val id = it.sportModeHeader.id
                    deleteSportModesUseCase.deleteSportMode(id)
                        .toSingleDefault(id)
                        .toFlowable()
                }
                .retryWhen { errors ->
                    handleError(errors)
                }
                .subscribeOn(ioThread)
                .observeOn(mainThread)
                .doOnTerminate {
                    sportModeHeaderItem.updateHeaderState(true, isListFull(), isListEmpty())
                    resetCurrentSportModesJson()
                }
                .subscribeBy(
                    onNext = { sportModeId ->
                        isDeleteInProgress = false
                        notifyChangesDone()
                        val newList = sportModeItemsList.filter {
                            if (it.sportModeHeader.id == sportModeId) {
                                trackSportModeRemoveOrDeleteEvents(it)
                            }
                            it.sportModeHeader.id != sportModeId
                        }
                        updateList(newList, notifyLoaded = true)
                    },
                    onError = {
                        isDeleteInProgress = false
                        notifyChangesDone()
                        updateList(sportModeItemsList, notifyLoaded = true)
                        Timber.d("failed to remove from the watch")
                    }
                )
        )
    }

    private fun getInUsingMultisportModeNames(sportModeHeader: SportModeHeader): String {
        if (sportModeHeader.modeIds.size > 1) return ""
        return sportModeItemsList.filter {
            !it.sportModeHeader.factoryMode
                && it.sportModeHeader.modeIds.size > 1
                && it.sportModeHeader.modeIds.contains(sportModeHeader.modeIds.first())
        }.mapNotNull { it.sportModeHeader.name }.joinToString("\n")
    }

    private fun resetCurrentSportModesJson() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            _currentSportModesJson = ""
            delay(3000) // Will fail to fetch if we do it after deleting immediately
            _currentSportModesJson = runSuspendCatching {
                fetchSportModesUseCase.fetchSportModesListJson().retry(3).await()
            }.onFailure {
                Timber.w(it, "Failed to fetch sport modes list json")
            }.getOrElse { "" }
        }
    }

    private fun isListFull() = sportModeItemsList.size >= LIST_MAX_SIZE

    private fun isListEmpty() = sportModeItemsList.none()

    override fun onSportModeItemClicked(item: SportModeItem) {
        sharedViewModel.activityId = item.sportModeHeader.activityHeader.id
        sharedViewModel.setModeIds(item.sportModeHeader.modeIds)
    }

    override fun onMultisportModeEditRequested(sportModeHeader: SportModeHeader?) {
        multisportModeEditRequested.postValue(
            MultisportModeEditRequest(
                _currentSportModesJson,
                sportModeHeader
            )
        )
    }

    override fun changeInDeletion(inDeletion: Boolean) {
        updateList(sportModeItemsList.map { it.copy(inDeletion = inDeletion) }, notifyLoaded = true)
        sportModeHeaderItem.inDeletion = inDeletion
        sportModeHeaderItem.updateEditButton()
    }

    override fun isDataLoaded(): Boolean {
        if (this::sportModeHeaderItem.isInitialized) {
            sportModeHeaderItem.updateHeaderState(!sharedViewModel.needsRefresh(), isListFull(), isListEmpty())
        }
        sharedViewModel.setClearModel(true)
        sharedViewModel.clear()
        return !sharedViewModel.needsRefresh()
    }

    override fun loadData() {
        sportModeHeaderItem = SportModeHeaderItem(
            loadFinished = false,
            listFull = false,
            listEmpty = true,
            deleteInProgressDelegate = this,
            sportModeSelectedListener = this,
        )
        updateList(listOf(), notifyLoaded = true)
        notifyDataLoading()
        viewModelScope.launch(coroutinesDispatchers.io) {
            val multisportIds = fetchSportModesUseCase.fetchMultiportIds()
            loadSportModeList(multisportIds)
        }
    }

    private fun loadSportModeList(multisportIds: List<Int>) {
        val startTime = System.currentTimeMillis()
        disposables.add(
            fetchSportModesUseCase.fetchCurrentSportModesListWithJson()
                .map { (json, list) ->
                    _currentSportModesJson = json
                    list.map {
                        val isCustomMultisportMode =
                            !it.factoryMode && it.activityHeader.id in multisportIds
                        SportModeItem(
                            sportModeHeader = it,
                            sportModeSelectedListener = this,
                            deleteInProgressDelegate = this,
                            isCustomMultisportMode = isCustomMultisportMode,
                        )
                    }
                }
                .retryWhen { errors ->
                    handleError(errors)
                }
                .observeOn(mainThread)
                .subscribe({ sportModes ->
                    updateList(
                        sportModes.map { it.copy(enabled = !isWatchBusy) },
                        notifyLoaded = true
                    )
                    sportModeHeaderItem.updateHeaderState(true, isListFull(), isListEmpty())
                    sharedViewModel.setModesLoaded()
                    trackSportModeLoadingTime(startTime, sportModes)
                    trackSportModeTooManyEvent(sportModes.size)
                }, { throwable ->
                    Timber.w(throwable, "Failed to fetch sport modes from the component")
                })
        )
    }

    private fun trackSportModeTooManyEvent(amountOfModes: Int) {
        if (amountOfModes == LIST_MAX_SIZE) {
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SPORT_MODE_TOO_MANY)
        }
    }

    private fun trackSportModeLoadingTime(startTime: Long, sportModes: List<SportModeItem>) {
        val endTime = System.currentTimeMillis()
        // Format duration in seconds using two decimals
        val loadTimePerMode =
            String.format(Locale.US, "%.2f", (endTime - startTime) / 1000.0f / sportModes.size)
        val properties = defaultWatchProperties().put(AnalyticsEventProperty.LOADING_TIME_PER_MODE, loadTimePerMode)
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SPORT_MODE_LOADING_TIME, properties)
    }

    private fun trackSportModeRemoveOrDeleteEvents(sportModeItem: SportModeItem) {
        if (sportModeItem.sportModeHeader.factoryMode) {
            val properties = AnalyticsProperties().apply {
                val stId = getStIdForMcId(sportModeItem.sportModeHeader.activityHeader.id)
                put(AnalyticsEventProperty.ACTIVITY_TYPE, ActivityType.valueOf(stId).simpleName)
            }
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.SPORT_MODE_REMOVE_FROM_SHORT_LIST,
                properties
            )
            emarsysAnalytics.trackEventWithProperties(
                AnalyticsEvent.SPORT_MODE_REMOVE_FROM_SHORT_LIST,
                properties.map
            )
        } else {
            amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SPORT_MODE_DELETE_CUSTOM_SPORT_MODE)
        }
    }

    fun updateWatchState(watchBusy: Boolean, watchSynchronizing: Boolean) {
        isWatchBusy = watchBusy
        if (!this::sportModeHeaderItem.isInitialized) {
            return
        }
        sportModeHeaderItem.updateWatchState(watchBusy, watchSynchronizing)
        val newList = sportModeItemsList.map { it.copy(enabled = !watchBusy) }
        updateList(newList, notifyLoaded = newList.isNotEmpty())
    }

    private fun checkMultisportCustomizationSupported() {
        viewModelScope.launch {
            fetchSportModesUseCase.supportsMultisportCustomization()
                .flowOn(coroutinesDispatchers.io)
                .catch {
                    Timber.w(it, "Failed to check multisport customization support")
                }
                .collect { supportMultisportCustomization ->
                    sportModeHeaderItem.updateMultisportButtonVisibility(supportMultisportCustomization)
                }
        }
    }

    companion object {
        const val LIST_MAX_SIZE = 20
    }
}

data class MultisportModeEditRequest(
    val currentSportModesJson: String,
    val sportModeHeader: SportModeHeader?
)
