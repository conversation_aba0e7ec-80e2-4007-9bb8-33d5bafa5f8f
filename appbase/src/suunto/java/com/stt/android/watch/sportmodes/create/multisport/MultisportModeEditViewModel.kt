package com.stt.android.watch.sportmodes.create.multisport

import android.content.Context
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.squareup.moshi.Moshi
import com.squareup.moshi.adapter
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.sportmodes.SportModesLocalDataSource
import com.stt.android.domain.sportmodes.FetchSportModesUseCase
import com.stt.android.domain.sportmodes.SportModeHeader
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.getMcIdForStId
import com.stt.android.infomodel.getStIdForMcId
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.sportmodes.base.SportModeConnectionWrapper
import com.stt.android.watch.sportmodes.mappers.SportModeJsonEditor
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import org.json.JSONObject
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class MultisportModeEditViewModel @Inject constructor(
    @ApplicationContext private val appContext: Context,
    private val savedStateHandle: SavedStateHandle,
    private val moshi: Moshi,
    private val fetchSportModesUseCase: FetchSportModesUseCase,
    private val saveMultisportModeUseCase: SaveMultisportModeUseCase,
    private val sportModesLocalDataSource: SportModesLocalDataSource,
    sportModeConnectionWrapper: SportModeConnectionWrapper,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    private val _currentSportModesJson = MutableStateFlow("")

    private val _state = MutableStateFlow(initialState())
    val state = _state.asStateFlow()

    val unableToEditReasonFlow = sportModeConnectionWrapper.unableToEditReasonFlow

    private val _intentChannel = Channel<MultisportModeEditIntent>(Channel.UNLIMITED)

    private val _events = Channel<MultisportModeEditEvent>(Channel.UNLIMITED)
    val events = _events.receiveAsFlow()

    private var _watchExistingTransitions = mutableListOf<MultisportChild.Transition>()
    private var _watchExistingSportModes = mutableListOf<MultisportChild.CustomSportMode>()

    init {
        initializeMultisportModeIfNeeded()
        handleIntents()
    }

    private fun initialState() = run {
        val jsonFromSavedState = savedStateHandle.get<String>(MultisportModeEditActivity.EXTRA_CURRENT_SPORT_MODES_JSON)
        _currentSportModesJson.update { jsonFromSavedState ?: "" }
        val modeJson = savedStateHandle.get<String>(MultisportModeEditActivity.EXTRA_SPORT_MODE)
        val sportModeHeader = modeJson?.let { json ->
            moshi.adapter<SportModeHeader>().fromJson(json)
        }
        if (sportModeHeader == null) {
            return@run MultisportModeEditViewState.SelectParent(
                multisportActivityTypes = emptyList(),
                loading = true
            )
        }
        val parentActivityType = sportModeHeader.activityHeader.id.let {
            val stId = getStIdForMcId(it)
            ActivityType.valueOf(stId)
        }
        val modeName = sportModeHeader.name ?: ""
        MultisportModeEditViewState.Confirm(
            parentActivityType = parentActivityType,
            selectedChildren = emptyList(),
            name = modeName,
            enableTransitions = false,
            loading = true,
            parentSportMode = sportModeHeader,
        )
    }

    private fun initializeMultisportModeIfNeeded() {
        val currentState = _state.value
        if (currentState !is MultisportModeEditViewState.Confirm) {
            handleIntent(MultisportModeEditIntent.Load)
            return
        }
        val parentSportMode = currentState.parentSportMode
        if (parentSportMode == null) return
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                Timber.d("initialize multisport mode header: $parentSportMode")
                val singleSportModes = getAllSingleSportModes()
                val transitionId = getTransitionId()
                val multisportChildren = parentSportMode.modeIds.mapNotNull { modeId ->
                    // Find child mode's header
                    val sportMode = singleSportModes.find { it.modeIds.first() == modeId }

                    val settings = fetchSportModesUseCase.fetchSportModeSettingsJson(modeId).await()
                    val displays = fetchSportModesUseCase.fetchSportModeDisplaysJson(modeId).await()
                    val activityId = getActivityIdFromSettings(settings)
                    val activityType = activityId?.let { ActivityType.valueOf(getStIdForMcId(it)) }

                    when {
                        activityId == transitionId -> {
                            MultisportChild.Transition(
                                modeId = modeId,
                                settings = settings,
                                displays = displays,
                            ).also {
                                _watchExistingTransitions.add(it)
                            }
                        }

                        sportMode != null -> {
                            activityType?.let { activityType ->
                                MultisportChild.CustomSportMode(
                                    activityType = activityType,
                                    sportMode = sportMode,
                                    settings = settings,
                                    displays = displays,
                                ).also {
                                    _watchExistingSportModes.add(it)
                                }
                            }
                        }

                        else -> { // Will not reach for now
                            activityType?.let { activityType ->
                                MultisportChild.ChildSportMode(
                                    activityType = activityType,
                                    modeId = modeId,
                                    settings = settings,
                                    displays = displays,
                                )
                            }
                        }
                    }.also { Timber.d("child group $modeId: $it") }
                }

                val hasTransitions = multisportChildren.any { it is MultisportChild.Transition }

                _state.update {
                    currentState.copy(
                        selectedChildren = multisportChildren,
                        enableTransitions = hasTransitions,
                        loading = false
                    )
                }
            }.onFailure {
                handleError(it, "Failed to initialize multisport mode")
                _state.update {
                    currentState.copy(loading = false)
                }
            }
        }
    }

    fun handleIntent(intent: MultisportModeEditIntent) {
        viewModelScope.launch {
            _intentChannel.send(intent)
        }
    }

    private fun handleIntents() {
        viewModelScope.launch {
            _intentChannel.receiveAsFlow().collect { intent ->
                when (intent) {
                    is MultisportModeEditIntent.Load -> loadParentActivities()
                    is MultisportModeEditIntent.SelectParent -> selectParentActivity(intent.activityType)
                    is MultisportModeEditIntent.SelectChild -> selectChild(intent.multisportChild)
                    is MultisportModeEditIntent.ConfirmSelectedChildren -> confirmSelectedChildren()
                    is MultisportModeEditIntent.SetName -> setName(intent.name)
                    is MultisportModeEditIntent.SetEnableTransitions -> setEnableTransitions(
                        intent.enableTransitions
                    )

                    is MultisportModeEditIntent.BackToParentSelection -> backToParentSelection()
                    is MultisportModeEditIntent.BackToChildSelection -> backToChildSelection()
                    is MultisportModeEditIntent.Confirm -> confirmCreation()
                    is MultisportModeEditIntent.ToggleEditing -> toggleEditing()
                    is MultisportModeEditIntent.ReorderChildren -> reorderChildren(
                        intent.fromIndex,
                        intent.toIndex
                    )
                }
            }
        }
    }

    private fun loadParentActivities() {
        _state.update { currentState ->
            when (currentState) {
                is MultisportModeEditViewState.SelectParent -> currentState.copy(loading = true)
                else -> currentState
            }
        }

        viewModelScope.launch(coroutinesDispatchers.io) {
            val activityTypes = getParentActivityTypes()
            _state.update {
                MultisportModeEditViewState.SelectParent(
                    multisportActivityTypes = activityTypes,
                    loading = false,
                )
            }
        }
    }

    private fun selectParentActivity(activityType: ActivityType) {
        _state.update { currentState ->
            when (currentState) {
                is MultisportModeEditViewState.SelectParent -> {
                    MultisportModeEditViewState.SelectChildren(
                        parentActivityType = activityType,
                        children = emptyList(),
                        selectedChildren = emptyList(),
                        loading = true,
                    )
                }

                else -> currentState
            }
        }
        viewModelScope.launch(coroutinesDispatchers.io) {
            val childActivityTypes = getChildActivityTypes(activityType)
            val childSportModes = getSingleSportModesForMultisport(childActivityTypes)
            val children = childSportModes.map { sportMode ->
                val activityType =
                    ActivityType.valueOf(getStIdForMcId(sportMode.activityHeader.id))
                _watchExistingSportModes.find {
                    it.sportMode == sportMode
                } ?: MultisportChild.CustomSportMode(
                    activityType = activityType,
                    sportMode = sportMode,
                )
            }
            _state.update { currentState ->
                when (currentState) {
                    is MultisportModeEditViewState.SelectChildren -> currentState.copy(
                        children = children,
                        loading = false,
                    )

                    else -> currentState
                }
            }
        }
    }

    private fun selectChild(multisportChild: MultisportChild) {
        if (_state.value is MultisportModeEditViewState.Confirm) {
            viewModelScope.launch {
                backToChildSelection()
                _events.send(MultisportModeEditEvent.ScrollToChild(multisportChild))
            }
            return
        }
        _state.update { currentState ->
            when (currentState) {
                is MultisportModeEditViewState.SelectChildren -> {
                    val isSelected = multisportChild in currentState.selectedChildren
                    val updatedSelection = if (isSelected) {
                        currentState.selectedChildren - multisportChild
                    } else {
                        currentState.selectedChildren + multisportChild
                    }
                    currentState.copy(
                        selectedChildren = fixChildrenWithTransitions(
                            updatedSelection,
                            currentState.enableTransitions
                        )
                    )
                }

                else -> currentState
            }
        }
    }

    private fun confirmSelectedChildren() {
        _state.update { currentState ->
            when (currentState) {
                is MultisportModeEditViewState.SelectChildren -> {
                    MultisportModeEditViewState.Confirm(
                        parentActivityType = currentState.parentActivityType,
                        selectedChildren = currentState.selectedChildren,
                        name = currentState.parentSportMode?.name ?: generateDefaultName(
                            currentState.parentActivityType
                        ),
                        enableTransitions = currentState.enableTransitions,
                        loading = false,
                        editing = false,
                        parentSportMode = currentState.parentSportMode,
                    )
                }

                else -> currentState
            }
        }
    }

    private fun setName(name: String) {
        _state.update { currentState ->
            when (currentState) {
                is MultisportModeEditViewState.Confirm -> currentState.copy(
                    name = name,
                    nameErrorTips = checkNameInvalidTips(name),
                )

                else -> currentState
            }
        }
    }

    private fun setEnableTransitions(enableTransitions: Boolean) {
        _state.update { currentState ->
            when (currentState) {
                is MultisportModeEditViewState.Confirm -> currentState.copy(
                    selectedChildren = fixChildrenWithTransitions(
                        currentState.selectedChildren,
                        enableTransitions
                    ),
                    enableTransitions = enableTransitions,
                )

                else -> currentState
            }
        }
    }

    private fun fixChildrenWithTransitions(
        selectedChildren: List<MultisportChild>,
        enableTransitions: Boolean
    ): List<MultisportChild> {
        if (enableTransitions) {
            if (selectedChildren.count { it.isTransition } == selectedChildren.size - 1) {
                return selectedChildren
            }
            var index = 0
            return selectedChildren.filterNot { it.isTransition }.flatMap { child ->
                val transition =
                    _watchExistingTransitions.getOrNull(index++)
                        ?: MultisportChild.EmptyTransition()
                listOf(child, transition)
            }.dropLast(1)
        } else {
            return selectedChildren.filterNot { it.isTransition }
        }
    }

    private fun toggleEditing() {
        _state.update { currentState ->
            when (currentState) {
                is MultisportModeEditViewState.Confirm -> currentState.copy(editing = !currentState.editing)
                else -> currentState
            }
        }
    }

    private fun reorderChildren(fromIndex: Int, toIndex: Int) {
        _state.update { currentState ->
            when (currentState) {
                is MultisportModeEditViewState.Confirm -> {
                    val childrenWithoutTransitions = currentState.selectedChildren.filterNot {
                        it.isTransition
                    }

                    val updatedChildren = childrenWithoutTransitions.toMutableList().apply {
                        if (fromIndex in 0 until size && toIndex in 0 until size) {
                            add(toIndex, removeAt(fromIndex))
                        }
                    }
                    val updatedChildrenWithTransitions = if (currentState.enableTransitions) {
                        val transitions = currentState.selectedChildren.filter { it.isTransition }
                        var index = 0
                        updatedChildren.flatMap {
                            val transition = transitions.getOrNull(index++)
                            if (transition != null) {
                                listOf(it, transition)
                            } else {
                                listOf(it)
                            }
                        }
                    } else {
                        updatedChildren.toList()
                    }
                    currentState.copy(selectedChildren = updatedChildrenWithTransitions)
                }

                else -> currentState
            }
        }
    }

    private suspend fun getParentActivityTypes(): List<ActivityType> {
        return runSuspendCatching {
            fetchSportModesUseCase.fetchMultiportIds().map { mcId ->
                ActivityType.valueOf(getStIdForMcId(mcId))
            }
        }.onFailure {
            handleError(it, "Failed to fetch multisport parent activities")
        }.getOrDefault(emptyList())
    }

    private suspend fun getChildActivityTypes(activityType: ActivityType): List<ActivityType> {
        return runSuspendCatching {
            val mcId = getMcIdForStId(activityType.id)
            fetchSportModesUseCase.fetchMultisportChildIds(mcId).map { mcId ->
                ActivityType.valueOf(getStIdForMcId(mcId))
            }
        }.onFailure {
            handleError(it, "Failed to fetch multisport child activities")
        }.getOrDefault(emptyList())
    }

    private suspend fun getAllSingleSportModes(): List<SportModeHeader> {
        return runSuspendCatching {
            val multisportIds = fetchSportModesUseCase.fetchMultiportIds()
            getAllSportModes()
                .filter { it.activityHeader.id !in multisportIds }
        }.onFailure {
            handleError(it, "Failed to fetch custom sport modes")
        }.getOrDefault(emptyList())
    }

    private suspend fun getSingleSportModesForMultisport(activityTypes: List<ActivityType>): List<SportModeHeader> {
        return runSuspendCatching {
            val supportedActivityIds = activityTypes.map { getMcIdForStId(it.id) }
            getAllSportModes()
                .filter { it.activityHeader.id in supportedActivityIds }
        }.onFailure {
            handleError(it, "Failed to fetch custom sport modes")
        }.getOrDefault(emptyList())
    }

    private suspend fun getAllSportModes(): List<SportModeHeader> {
        return runSuspendCatching {
            _currentSportModesJson.value.takeIf { it.isNotEmpty() }?.let {
                sportModesLocalDataSource.fetchCurrentSportModesList(it).await()
            } ?: run {
                val (json, list) = fetchSportModesUseCase.fetchCurrentSportModesListWithJson()
                    .await()
                _currentSportModesJson.update { json }
                list
            }
        }.getOrDefault(emptyList())
    }

    private fun backToParentSelection() {
        _state.update { currentState ->
            when (currentState) {
                is MultisportModeEditViewState.SelectChildren -> {
                    MultisportModeEditViewState.SelectParent(
                        multisportActivityTypes = emptyList(),
                        loading = true
                    )
                }

                else -> currentState
            }
        }

        val currentState = _state.value
        if (currentState is MultisportModeEditViewState.SelectParent) {
            loadParentActivities()
        }
    }

    private suspend fun backToChildSelection() {
        _state.update { currentState ->
            when (currentState) {
                is MultisportModeEditViewState.Confirm -> {
                    val childActivityTypes =
                        getChildActivityTypes(currentState.parentActivityType)
                    val childSportModes =
                        getSingleSportModesForMultisport(childActivityTypes)
                    val children = childSportModes.map { sportMode ->
                        val activityType =
                            ActivityType.valueOf(getStIdForMcId(sportMode.activityHeader.id))
                        _watchExistingSportModes.find {
                            it.sportMode == sportMode
                        } ?: MultisportChild.CustomSportMode(
                            activityType = activityType,
                            sportMode = sportMode,
                        )
                    }
                    val selectedChildren = currentState.selectedChildren
                    MultisportModeEditViewState.SelectChildren(
                        parentActivityType = currentState.parentActivityType,
                        children = children,
                        selectedChildren = selectedChildren,
                        loading = false,
                        parentSportMode = currentState.parentSportMode,
                        enableTransitions = currentState.enableTransitions,
                    )
                }

                else -> currentState
            }
        }
    }

    private fun confirmCreation() {
        val currentState = _state.value
        if (currentState !is MultisportModeEditViewState.Confirm) return
        _state.update { currentState.copy(loading = true) }
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                saveMultisportModeUseCase(
                    parentActivityType = currentState.parentActivityType,
                    multisportChildren = currentState.selectedChildren,
                    name = currentState.name.trim(),
                    existingSportMode = currentState.parentSportMode,
                    watchSportModes = _currentSportModesJson.value.takeIf { it.isNotEmpty() },
                )
                _state.update { currentState.copy(loading = false) }
                _events.send(MultisportModeEditEvent.Saved)
            }.onFailure {
                _state.update { currentState.copy(loading = false) }
                handleError(it, "Failed to save multisport mode")
            }
        }
    }

    private fun generateDefaultName(parentActivityType: ActivityType): String {
        return appContext.getString(
            R.string.custom_sport_mode_default_name,
            parentActivityType.getLocalizedName(appContext.resources),
        )
    }

    private fun checkNameInvalidTips(name: String): Int {
        val bytes = name.trim().toByteArray(Charsets.UTF_8)
        return when {
            bytes.size > MAX_BYTES_OF_NAME -> R.string.sport_mode_name_long_error
            bytes.isEmpty() -> R.string.sport_mode_name_short_error
            else -> 0
        }
    }

    private suspend fun getTransitionId() = fetchSportModesUseCase.getTransitionId()

    private fun getActivityIdFromSettings(json: String): Int? =
        runCatching {
            val root = JSONObject(json)
            val settings = root.getJSONObject(SportModeJsonEditor.SETTINGS)
            val customModes = settings.getJSONArray(SportModeJsonEditor.CUSTOM_MODES)
            if (customModes.length() == 0) {
                null
            } else {
                val firstMode = customModes.getJSONObject(0)
                firstMode.getInt(SportModeJsonEditor.ACTIVITY_ID)
            }
        }.getOrNull()

    private suspend fun handleError(throwable: Throwable, message: String? = null) {
        if (throwable is MissingCurrentWatchException) {
            Timber.d(throwable, "Missing current watch")
            return
        }
        Timber.w(throwable, message)
        _events.send(MultisportModeEditEvent.Error(throwable))
    }

    private companion object {
        private const val MAX_BYTES_OF_NAME = 32
    }
}
