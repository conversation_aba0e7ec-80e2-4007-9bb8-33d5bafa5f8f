package com.stt.android.watch.preference

import android.graphics.Color
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.stt.android.utils.argument

class DummyFragment : Fragment() {
    var argType: String by argument()
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return FrameLayout(requireContext()).apply {
            setBackgroundColor(Color.LTGRAY)
            addView(
                TextView(requireContext()).apply {
                    layoutParams = FrameLayout.LayoutParams(-1, -1)
                    text = argType
                    gravity = Gravity.CENTER
                }
            )
        }
    }

    companion object {
        fun instance(type: String): DummyFragment = DummyFragment().apply {
            argType = type
        }
    }
}
