package com.stt.android.watch.sportmodes.list

import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.widget.Button
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.navigation.findNavController
import com.stt.android.R
import com.stt.android.common.ui.BaseBindableItem
import com.stt.android.databinding.ItemSportmodesWatchHeaderBinding

data class SportModeHeaderItem(
    var loadFinished: <PERSON>olean,
    var listFull: <PERSON>olean,
    var listEmpty: <PERSON>olean,
    val deleteInProgressDelegate: DeleteInProgressDelegate,
    private val sportModeSelectedListener: OnSportModeSelectedListener,
    var inDeletion: Boolean = false,
) : BaseBindableItem<ItemSportmodesWatchHeaderBinding>() {
    private lateinit var createSportModeButton: Button
    private lateinit var createMultisportModeButton: Button
    private lateinit var listFullText: View
    private lateinit var editButton: TextView
    private lateinit var shortListText: View

    private var watchBusy = false
    private var watchSynchronizing = false

    private var supportMultisportCustomization = false

    fun navigateToCreate(view: View) {
        view.findNavController().navigate(SportModeListFragmentDirections.sportModeCreateAction())
    }

    fun navigateToCreateMultisport(view: View) {
        sportModeSelectedListener.onMultisportModeEditRequested(null)
    }

    override fun bind(viewBinding: ItemSportmodesWatchHeaderBinding, position: Int) {
        super.bind(viewBinding, position)
        createSportModeButton = viewBinding.newCustomModeButton
        createMultisportModeButton = viewBinding.newCustomMultisportModeButton
        listFullText = viewBinding.sportmodesMaxAmount
        editButton = viewBinding.editButton
        shortListText = viewBinding.shortListText
        updateHeaderState(loadFinished, listFull, listEmpty)

        createSportModeButton.setText(
            if (supportMultisportCustomization) {
                R.string.sport_mode
            } else {
                R.string.sport_modes_new_custom_mode
            }
        )
    }

    fun updateHeaderState(loadFinished: Boolean, listFull: Boolean, listEmpty: Boolean) {
        this.loadFinished = loadFinished
        this.listFull = listFull
        this.listEmpty = listEmpty
        if (!isBound()) {
            return
        }
        createSportModeButton.isEnabled = loadFinished && !listFull &&
            !deleteInProgressDelegate.isDeletionInProcess() && !watchBusy && !watchSynchronizing
        createMultisportModeButton.isEnabled = loadFinished && !listFull &&
            !deleteInProgressDelegate.isDeletionInProcess() && !watchBusy && !watchSynchronizing
        shortListText.visibility = if (loadFinished && !listEmpty) VISIBLE else GONE
        updateEditButton()
        listFullText.visibility = if (listFull) VISIBLE else GONE
    }

    fun updateWatchState(watchBusy: Boolean, watchSynchronizing: Boolean) {
        this.watchBusy = watchBusy
        this.watchSynchronizing = watchSynchronizing
        updateHeaderState(loadFinished, listFull, listEmpty)
    }

    fun updateMultisportButtonVisibility(supportMultisportCustomization: Boolean) {
        createMultisportModeButton.visibility =
            if (supportMultisportCustomization) VISIBLE else GONE
        this.supportMultisportCustomization = supportMultisportCustomization
        createSportModeButton.setText(
            if (supportMultisportCustomization) {
                R.string.sport_mode
            } else {
                R.string.sport_modes_new_custom_mode
            }
        )
    }

    override fun getLayout() = R.layout.item_sportmodes_watch_header

    @Suppress("UNUSED_PARAMETER")
    fun startModesSelection(view: View) {
        sportModeSelectedListener.changeInDeletion(!inDeletion)
    }

    fun updateEditButton() {
        editButton.isEnabled = !watchBusy && !watchSynchronizing
        editButton.isVisible = loadFinished && !listEmpty && !deleteInProgressDelegate.isDeletionInProcess()
        editButton.setText(
            if (inDeletion) R.string.done else R.string.edit
        )
    }

    private fun isBound(): Boolean {
        return this::createSportModeButton.isInitialized &&
            this::createMultisportModeButton.isInitialized &&
            this::listFullText.isInitialized &&
            this::editButton.isInitialized &&
            this::shortListText.isInitialized
    }
}
