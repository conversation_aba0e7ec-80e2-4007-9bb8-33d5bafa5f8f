package com.stt.android.watch.preference

import android.os.Parcelable
import com.stt.android.utils.STTConstants
import com.suunto.connectivity.notifications.NotificationState
import kotlinx.parcelize.Parcelize
import kotlin.time.Duration
import kotlin.time.DurationUnit
import kotlin.time.ExperimentalTime

interface SetupPreferenceReducer {
    val reduce: suspend (SetupPreference) -> SetupPreference
}

interface ISetupPreferenceActivity

@Parcelize
data class SetupPreference(
    val ignored: Boolean = false,
    val direction: PreferenceDirection = PreferenceDirection.RIGHT,
    val notifications: PreferenceNotification = PreferenceNotification(),
    val personalInfo: PreferencePersonalInfo = PreferencePersonalInfo(),
    val goalSetting: PreferenceGoalSetting = PreferenceGoalSetting()
) : Parcelable {
    companion object {
        val ignoredInstance: SetupPreference
            get() = SetupPreference(ignored = true)
    }
}

enum class PreferenceDirection {
    LEFT, RIGHT
}

@Parcelize
data class PreferenceNotification(
    val enable: Boolean = true,
    val appEnable: Boolean = true,
    val appNotifications: List<PreferenceAppNotification> = emptyList(),
    val callEnable: Boolean = true,
    val msgEnable: Boolean = true,
    val predefinedReplies: List<String> = emptyList(),
) : Parcelable {
    fun toNotificationState(): NotificationState {
        return NotificationState(
            enabled = enable,
            callEnabled = callEnable,
            smsEnabled = msgEnable,
            applicationEnabled = appEnable,
        )
    }
}

@Parcelize
data class PreferenceAppNotification(
    val enable: Boolean = true,
    val packageName: String = "",
) : Parcelable

@Parcelize
data class PreferencePersonalInfo(
    val height: String = "170",
    val weight: String = "70000",
    val gender: String = "female",
    val birthYear: String = "1990",
    val measureUnit: String = "metric",
    val startOfWeek: String = "monday",
) : Parcelable

@OptIn(ExperimentalTime::class)
@Parcelize
data class PreferenceGoalSetting(
    val weeklyTraining: Int = STTConstants.SuuntoPreferences.KEY_SUUNTO_WATCH_WEEKLY_TARGET_DURATION_DEFAULT,
    val dailySteps: Int = 10_000,
    val dailyKcal: Int = 500,
    val dailySleep: Int = Duration.convert(8.0, DurationUnit.HOURS, DurationUnit.SECONDS).toInt(),
) : Parcelable
