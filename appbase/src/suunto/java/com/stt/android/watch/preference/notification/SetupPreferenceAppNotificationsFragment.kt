package com.stt.android.watch.preference.notification

import androidx.fragment.app.viewModels
import com.stt.android.ui.activities.settings.watch.notifications.BaseWatchAppNotificationsPermissionsFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SetupPreferenceAppNotificationsFragment :
    BaseWatchAppNotificationsPermissionsFragment<SetupPreferenceAppNotificationsViewModel>() {
    override val viewModel: SetupPreferenceAppNotificationsViewModel by viewModels()
}
