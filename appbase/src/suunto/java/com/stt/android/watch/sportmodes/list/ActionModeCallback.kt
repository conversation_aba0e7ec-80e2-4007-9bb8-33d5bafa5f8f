package com.stt.android.watch.sportmodes.list

/**
 * This interface is implemented by view model of the fragment with the list of sport mode
 * @see [SportModesListViewModel]
 * Reference is held by shared view model that keeps the state of sport modes
 */
interface ActionModeCallback {
    fun onDeleteClicked()

    fun onActionModeToggled(resetSportModeItemsState: Boolean)

    fun getSelectedModesAmount(): Int
}
