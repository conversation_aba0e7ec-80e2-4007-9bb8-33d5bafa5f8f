package com.stt.android.watch.sportmodes.editfield

import android.view.View
import androidx.navigation.findNavController
import com.stt.android.R
import com.stt.android.common.ui.ClickableItem
import com.stt.android.databinding.ItemSportmodeFieldBinding
import com.xwray.groupie.databinding.GroupieViewHolder
import io.reactivex.disposables.Disposable
import timber.log.Timber

data class SportModeFieldItem(
    val fieldId: String,
    val fieldName: String,
    private val sportModeFieldChangeDelegate: SportModeFieldChangeDelegate
) : ClickableItem<ItemSportmodeFieldBinding>() {
    var disposable: Disposable? = null

    override fun getLayout() = R.layout.item_sportmode_field

    override fun onClick(view: View) {
        disposable = sportModeFieldChangeDelegate.pickField(fieldId)
            .subscribe({
                val navController = view.findNavController()
                navController.popBackStack(R.id.sportModeEditDisplaysFragment, false)
            }, {
                Timber.w(it)
            })
    }

    override fun unbind(holder: GroupieViewHolder<ItemSportmodeFieldBinding>) {
        super.unbind(holder)
        disposable?.let {
            if (!it.isDisposed) {
                it.dispose()
            }
        }
    }
}
