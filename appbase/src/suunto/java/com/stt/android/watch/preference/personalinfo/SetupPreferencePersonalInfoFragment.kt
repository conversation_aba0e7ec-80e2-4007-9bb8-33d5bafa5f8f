package com.stt.android.watch.preference.personalinfo

import android.content.SharedPreferences
import android.os.Bundle
import androidx.core.content.edit
import androidx.preference.Preference
import com.stt.android.R
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.user.UserSettings
import com.stt.android.home.settings.ExtendedPreferenceFragmentCompat
import com.stt.android.watch.preference.PreferencePersonalInfo

class SetupPreferencePersonalInfoFragment : ExtendedPreferenceFragmentCompat() {

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        setPreferencesFromResource(R.xml.setup_preference_personal_info, rootKey)

        findPreference<Preference>(KEY_UNIT)?.setOnPreferenceChangeListener { _, newValue ->
            val weightPreference =
                findPreference<SetupPreferenceWeightPreference>(KEY_WEIGHT)
            weightPreference?.refresh(newValue as String)
            val heightPreference =
                findPreference<SetupPreferenceHeightPreference>(KEY_HEIGHT)
            heightPreference?.refresh(newValue as String)
            true
        }
    }

    companion object {
        private const val KEY_WEIGHT = "setup_preference_weight"
        private const val KEY_HEIGHT = "setup_preference_height"
        private const val KEY_GENDER = "setup_preference_gender"
        private const val KEY_BIRTH = "setup_preference_birth_date"
        const val KEY_UNIT = "setup_preference_measurement_unit"
        private const val KEY_START_OF_WEEK = "setup_preference_firstDayOfTheWeek"
        private const val defaultWeight = UserSettings.DEFAULT_WEIGHT_GRAMS.toString()
        private const val defaultHeight = UserSettings.DEFAULT_HEIGHT_CENTIMETERS.toString()
        private val defaultBirth = UserSettings.DEFAULT_BIRTH_DATE.toString()
        fun reset(
            sharedPreferences: SharedPreferences,
            userSettingsController: UserSettingsController
        ) {
            val weight = userSettingsController.settings.weight.takeIf { it > 0 }?.toString()
                ?: defaultWeight
            val height = userSettingsController.settings.height.takeIf { it > 0 }?.toString()
                ?: defaultHeight
            val gender = userSettingsController.settings.gender.name.lowercase()
            val birth = userSettingsController.settings.birthDate.takeIf { it > 0 }?.toString()
                ?: defaultBirth
            val unit = userSettingsController.settings.measurementUnit.name.lowercase()
            val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek.name.lowercase()
            sharedPreferences.edit {
                putString(KEY_WEIGHT, weight)
                putString(KEY_HEIGHT, height)
                putString(KEY_GENDER, gender)
                putString(KEY_BIRTH, birth)
                putString(KEY_UNIT, unit)
                putString(KEY_START_OF_WEEK, firstDayOfWeek)
            }
        }

        fun retrieve(
            sharedPreferences: SharedPreferences,
            userSettingsController: UserSettingsController
        ): PreferencePersonalInfo {
            val weight = userSettingsController.settings.weight.takeIf { it > 0 }?.toString()
                ?: defaultWeight
            val height = userSettingsController.settings.height.takeIf { it > 0 }?.toString()
                ?: defaultHeight
            val gender = userSettingsController.settings.gender.name.lowercase()
            val birth = userSettingsController.settings.birthDate.takeIf { it > 0 }?.toString()
                ?: defaultBirth
            val unit = userSettingsController.settings.measurementUnit.name.lowercase()
            val firstDayOfWeek = userSettingsController.settings.firstDayOfTheWeek.name.lowercase()
            return PreferencePersonalInfo(
                weight = sharedPreferences.getString(KEY_WEIGHT, weight) ?: weight,
                height = sharedPreferences.getString(KEY_HEIGHT, height) ?: height,
                gender = sharedPreferences.getString(KEY_GENDER, gender) ?: gender,
                birthYear = sharedPreferences.getString(KEY_BIRTH, birth) ?: birth,
                measureUnit = sharedPreferences.getString(KEY_UNIT, unit) ?: unit,
                startOfWeek = sharedPreferences.getString(KEY_START_OF_WEEK, firstDayOfWeek)
                    ?: firstDayOfWeek
            )
        }
    }
}
