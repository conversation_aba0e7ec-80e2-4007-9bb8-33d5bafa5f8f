package com.stt.android.watch.sportmodes.create.multisport.composable

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.stt.android.watch.sportmodes.create.multisport.MultisportChild
import com.stt.android.watch.sportmodes.create.multisport.MultisportModeEditIntent
import com.stt.android.watch.sportmodes.create.multisport.MultisportModeEditViewState

@Composable
fun MultisportModeEditContent(
    state: MultisportModeEditViewState,
    onIntent: (MultisportModeEditIntent) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    scrollToChild: MultisportChild? = null,
    onScrollToChildConsumed: () -> Unit = {},
) {
    when (state) {
        is MultisportModeEditViewState.SelectParent -> {
            SelectParentContent(state, onIntent, modifier)
        }

        is MultisportModeEditViewState.SelectChildren -> {
            SelectChildrenContent(
                state = state,
                onIntent = onIntent,
                modifier = modifier,
                scrollToChild = scrollToChild,
                onScrollToChildConsumed = onScrollToChildConsumed
            )
        }

        is MultisportModeEditViewState.Confirm -> {
            ConfirmContent(state, onIntent, modifier, enabled)
        }
    }
}
