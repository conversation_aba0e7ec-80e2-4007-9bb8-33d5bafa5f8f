package com.stt.android.watch.preference

import android.annotation.SuppressLint
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.view.MenuItem
import androidx.activity.result.contract.ActivityResultContract
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.stt.android.R
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.home.dashboard.bottomsheet.WeeklyGoalBottomSheetFragment
import com.stt.android.utils.activityresult.ResultLauncherActivity
import com.stt.android.utils.firstOfType
import com.stt.android.watch.MissingCurrentWatchException
import com.stt.android.watch.SuuntoWatchModel
import com.stt.android.watch.preference.direction.SetupPreferenceDirectionFragment
import com.stt.android.watch.preference.goal.SetupPreferenceGoalFragment
import com.stt.android.watch.preference.notification.EnableNotificationReducer
import com.stt.android.watch.preference.notification.SetupPreferenceNotificationsFragment
import com.stt.android.watch.preference.personalinfo.SetupPreferencePersonalInfoFragment
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class SetupPreferenceActivity : ResultLauncherActivity(), ISetupPreferenceActivity,
    WeeklyGoalBottomSheetFragment.BottomSheetUpdateListener,
    SimpleDialogFragment.Callback {
    private val viewModel: SetupPreferenceViewModel by viewModels()

    private val titles: List<Int> = listOf(
        0,
        R.string.setup_preference_direction_title,
        R.string.setup_preference_personal_info_title,
        R.string.target_setting_preference_title,
    )

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var userSettingsController: UserSettingsController

    @Inject
    lateinit var suuntoWatchModel: SuuntoWatchModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContentWithTheme {
            SetupPreferenceScreen(
                titles = titles,
                fragments = listOf(
                    SetupPreferenceNotificationsFragment(),
                    SetupPreferenceDirectionFragment(),
                    SetupPreferencePersonalInfoFragment(),
                    SetupPreferenceGoalFragment(),
                ),
                onBackPressed = ::onBackPressed,
                onFinish = ::finishWithSetupPreference
            )
        }

        SetupPreferencePersonalInfoFragment.reset(sharedPreferences, userSettingsController)

        showNotificationDialog()

        subscribeWatchCancel()
    }

    private fun subscribeWatchCancel() {
        lifecycleScope.launch {
            runSuspendCatching {
                suuntoWatchModel.subscribeSetupPreferenceCancel().collect {
                    Timber.d("watch setup preference cancel info: $it")
                    if (it.status == 1) {
                        showCancelledOnWatchDialog()
                    }
                }
            }.onFailure {
                if (it !is MissingCurrentWatchException) {
                    Timber.w(it, "subscribe state error")
                }
            }
        }
    }

    private fun finishWithSetupPreference(ignore: Boolean = false) {
        val setupPreference = if (ignore) {
            SetupPreference.ignoredInstance
        } else {
            viewModel.stateFlow.value.copy(
                personalInfo = SetupPreferencePersonalInfoFragment.retrieve(
                    sharedPreferences,
                    userSettingsController
                )
            )
        }
        Timber.d("parseResult: %s", setupPreference)
        lifecycleScope.launch {
            setResult(RESULT_OK, Intent().apply {
                putExtra(
                    EXTRA_SETUP_PREFERENCE,
                    setupPreference
                )
            })
            finish()
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            onBackPressed()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {
        showSkipDialog()
    }

    override fun onDialogButtonPressed(tag: String?, which: Int) {
        when (tag) {
            SKIP_DIALOG_TAG -> {
                if (which == DialogInterface.BUTTON_NEGATIVE) {
                    finishWithSetupPreference(ignore = true)
                }
            }
            NOTIFICATION_DIALOG_TAG -> {
                viewModel.invokeReducer(EnableNotificationReducer(which == DialogInterface.BUTTON_POSITIVE))
            }
            CANCELLED_ON_WATCH_DIALOG_TAG -> {
                finishWithSetupPreference(ignore = true)
            }
            else -> {
                // do nothing
            }
        }
    }

    override fun onDialogDismissed(tag: String?) {
        if (tag == CANCELLED_ON_WATCH_DIALOG_TAG) {
            finishWithSetupPreference(ignore = true)
        }
    }

    override fun onWeeklyTargetDurationChanged(weeklyTarget: Int) {
        supportFragmentManager.fragments
            .firstOfType<WeeklyGoalBottomSheetFragment.BottomSheetUpdateListener>()
            ?.onWeeklyTargetDurationChanged(weeklyTarget)
    }

    override fun onWeeklyTargetSettingDismissed() {
        supportFragmentManager.fragments
            .firstOfType<WeeklyGoalBottomSheetFragment.BottomSheetUpdateListener>()
            ?.onWeeklyTargetSettingDismissed()
    }

    override fun onCoachEnabled(enabled: Boolean) {
        supportFragmentManager.fragments
            .firstOfType<WeeklyGoalBottomSheetFragment.BottomSheetUpdateListener>()
            ?.onCoachEnabled(enabled)
    }

    private fun showSkipDialog() {
        SimpleDialogFragment.newInstance(
            message = getString(R.string.setup_preference_skip_description),
            title = getString(R.string.setup_preference_skip_title),
            positiveButtonText = getString(R.string.no),
            negativeButtonText = getString(R.string.yes),
        ).show(supportFragmentManager, SKIP_DIALOG_TAG)
    }

    private fun showNotificationDialog() {
        SimpleDialogFragment.newInstance(
            message = getString(R.string.setup_preference_notification_dialog_description),
            title = getString(R.string.setup_preference_notification_dialog_title),
            positiveButtonText = getString(R.string.allow),
            negativeButtonText = getString(R.string.dont_allow),
        ).show(supportFragmentManager, NOTIFICATION_DIALOG_TAG)
    }

    private fun showCancelledOnWatchDialog() {
        SimpleDialogFragment.newInstance(
            message = getString(R.string.setup_preference_cancelled_on_watch_description),
            title = getString(R.string.setup_preference_cancelled_on_watch_title),
            positiveButtonText = getString(R.string.understand),
            cancellable = false,
        ).show(supportFragmentManager, CANCELLED_ON_WATCH_DIALOG_TAG)
    }

    class ResultContract : ActivityResultContract<Intent, Result<SetupPreference>>() {
        override fun createIntent(context: Context, input: Intent): Intent = input.apply {
            setClass(context, SetupPreferenceActivity::class.java)
        }

        override fun parseResult(resultCode: Int, intent: Intent?): Result<SetupPreference> {
            return (intent?.getParcelableExtra<SetupPreference>(EXTRA_SETUP_PREFERENCE))?.let {
                Result.success(it)
            } ?: Result.failure(IllegalStateException("No setup preference got"))
        }
    }

    companion object {
        const val EXTRA_SETUP_PREFERENCE = "setup_preference"
        private const val SKIP_DIALOG_TAG = "skip_dialog"
        private const val NOTIFICATION_DIALOG_TAG = "notification_dialog"
        private const val CANCELLED_ON_WATCH_DIALOG_TAG = "cancelled_on_watch_dialog"
    }
}
