package com.stt.android.watch.sportmodes.fte

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.NavHostFragment
import com.stt.android.R
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.common.ui.observeK
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SportModeFteFragment : ViewModelFragment2() {

    override val viewModel: SportModeFteViewModel by viewModels()

    override fun getLayoutResId(): Int {
        return R.layout.fragment_sportmodes_fte
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        viewModel.continueToSportModesEvent.observeK(viewLifecycleOwner) {
            (activity as AppCompatActivity).supportActionBar?.show()
            NavHostFragment.findNavController(this).navigate(R.id.sportModeListFragmentAction)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        (activity as AppCompatActivity).supportActionBar?.hide()
        return super.onCreateView(inflater, container, savedInstanceState)
    }
}
