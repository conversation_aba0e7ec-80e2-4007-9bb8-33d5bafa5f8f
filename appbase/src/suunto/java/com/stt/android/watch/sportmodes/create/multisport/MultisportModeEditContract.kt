package com.stt.android.watch.sportmodes.create.multisport

import android.content.res.Resources
import com.stt.android.domain.sportmodes.SportModeHeader
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.getMcIdForStId

private const val MAX_COUNT_WHEN_TRANSITIONS_ENABLED = 3
private const val MAX_COUNT = 5
private const val MIN_COUNT = 2

sealed class MultisportModeEditViewState {
    data class SelectParent(
        val multisportActivityTypes: List<ActivityType>,
        val loading: Boolean,
    ) : MultisportModeEditViewState()

    data class SelectChildren(
        val parentActivityType: ActivityType,
        val children: List<MultisportChild>,
        val selectedChildren: List<MultisportChild>,
        val loading: Boolean,
        val parentSportMode: SportModeHeader? = null,
        val enableTransitions: Boolean = false,
    ) : MultisportModeEditViewState() {

        val reachedMinSelectedCount: Boolean
            get() = selectedChildren.filterNot { it.isTransition }.size >= MIN_COUNT
        val reachedMaxSelectedCount: Boolean
            get() = selectedChildren.filterNot { it.isTransition }.size >= (if (enableTransitions) MAX_COUNT_WHEN_TRANSITIONS_ENABLED else MAX_COUNT)
    }

    data class Confirm(
        val parentActivityType: ActivityType,
        val selectedChildren: List<MultisportChild>,
        val name: String,
        val enableTransitions: Boolean,
        val loading: Boolean,
        val parentSportMode: SportModeHeader? = null,
        val editing: Boolean = false,
        val nameErrorTips: Int = 0,
    ) : MultisportModeEditViewState() {

        val reachedMinSelectedCount: Boolean
            get() = selectedChildren.filterNot { it.isTransition }.size >= MIN_COUNT
        val canEnableTransitions: Boolean
            get() = selectedChildren.filterNot { it.isTransition }.size <= MAX_COUNT_WHEN_TRANSITIONS_ENABLED
    }
}

sealed interface MultisportModeEditIntent {
    data object Load : MultisportModeEditIntent
    data class SelectParent(val activityType: ActivityType) : MultisportModeEditIntent
    data class SelectChild(val multisportChild: MultisportChild) : MultisportModeEditIntent
    data object ConfirmSelectedChildren : MultisportModeEditIntent
    data class SetName(val name: String) : MultisportModeEditIntent
    data class SetEnableTransitions(val enableTransitions: Boolean) : MultisportModeEditIntent
    data object BackToParentSelection : MultisportModeEditIntent
    data object BackToChildSelection : MultisportModeEditIntent
    data object Confirm : MultisportModeEditIntent
    data object ToggleEditing : MultisportModeEditIntent
    data class ReorderChildren(val fromIndex: Int, val toIndex: Int) : MultisportModeEditIntent
}

sealed interface MultisportModeEditEvent {
    data object Saved : MultisportModeEditEvent
    data class Error(
        val throwable: Throwable,
    ) : MultisportModeEditEvent
    data class ScrollToChild(
        val multisportChild: MultisportChild,
    ) : MultisportModeEditEvent
}

sealed class MultisportChild {

    abstract val activityType: ActivityType?
    val iconId: Int?
        get() = activityType?.iconId

    open val modeId: Int? = null
    open val settings: String? = null
    open val displays: String? = null
    open val modeType: String? = null

    open fun getLocalizedName(resources: Resources): String {
        return ""
    }

    open val isTransition: Boolean
        get() = false

    val mcId: Int?
        get() = activityType?.id?.let { getMcIdForStId(it) }

    /**
     * Selection in "All sports"
     *
     * Note: will not in multisport feature phase 1
     */
    data class ActivityTypeOnly(
        override val activityType: ActivityType,
    ) : MultisportChild() {

        override fun getLocalizedName(resources: Resources): String {
            return activityType.getLocalizedName(resources)
        }
    }

    /**
     * Selection for creation
     */
    data class CustomSportMode(
        override val activityType: ActivityType,
        val sportMode: SportModeHeader,
        override val settings: String? = null,
        override val displays: String? = null,
    ) : MultisportChild() {
        override fun getLocalizedName(resources: Resources): String {
            return sportMode.name ?: activityType.getLocalizedName(resources)
        }

        override val modeId: Int?
            get() = sportMode.modeIds.first()

        override val modeType: String?
            get() = sportMode.activityHeader.type
    }

    /**
     * Existing child sport mode in multisport mode
     */
    data class ChildSportMode(
        override val activityType: ActivityType,
        override val modeId: Int,
        override val settings: String,
        override val displays: String,
        val sportMode: SportModeHeader? = null, // If sport mode is also independent mode, it will be not null
    ) : MultisportChild() {
        override fun getLocalizedName(resources: Resources): String {
            return sportMode?.name ?: activityType.getLocalizedName(resources)
        }

        override val modeType: String?
            get() = sportMode?.activityHeader?.type
    }

    /**
     * New transition for creation
     */
    data class EmptyTransition(
        override val activityType: ActivityType? = null
    ) : MultisportChild() {
        override val isTransition: Boolean
            get() = true
    }

    /**
     * Existing transition in multisport mode, similar to ChildSportMode
     */
    data class Transition(
        override val activityType: ActivityType? = null,
        override val modeId: Int,
        override val settings: String,
        override val displays: String,
    ) : MultisportChild() {
        override val isTransition: Boolean
            get() = true
    }
}
