package com.stt.android.watch.sportmodes.create

import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.ui.content.Resources
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.sportmodes.FetchSportModesUseCase
import com.stt.android.domain.sportmodes.WatchSportMode
import com.stt.android.domain.workout.ActivityType
import com.stt.android.infomodel.getStIdForMcId
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.watch.sportmodes.SportModeViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import io.reactivex.Single
import io.reactivex.disposables.Disposable
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class SportModeCreateViewModel
@Inject constructor(
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    private val sportModesUseCase: FetchSportModesUseCase,
    private val resources: Resources,
    private val emarsysAnalytics: EmarsysAnalytics,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker
) : SportModeViewModel(ioThread, mainThread), SportModeActivityPickerDelegate {

    data class ActionParams(
        val sportModeId: Int,
        val activityName: String
    )

    val openSportModeEditDisplay = SingleLiveEvent<ActionParams>()

    private val alphabeticalSort = { item1: SportModeCreateItem, item2: SportModeCreateItem ->
        resources.getString(item1.activityNameResId).compareTo(resources.getString(item2.activityNameResId))
    }

    private var selectSportModeDisposable: Disposable? = null

    private val onSelectSportModeItem = { activityId: Int, activityName: String ->
        if (selectSportModeDisposable == null || selectSportModeDisposable?.isDisposed == true) {
            selectSportModeDisposable = pickActivity(activityId)
                .subscribe({ watchSportMode ->
                    openSportModeEditDisplay.postValue(ActionParams(watchSportMode.modes[0].id, activityName))
                }, {
                    Timber.w(it)
                }).apply { disposables.add(this) }
        }
    }

    override fun pickActivity(activityId: Int): Single<WatchSportMode> {
        notifyChangesInProgress()
        return sportModesUseCase.fetchSportModeTemplate(activityId)
            .doOnSuccess { watchSportMode: WatchSportMode ->
                sharedViewModel.activityId = activityId
                sharedViewModel.setGroup(watchSportMode.group, true)
                sharedViewModel.updateGroupRelay()
                sharedViewModel.setModes(watchSportMode.modes, true)
                trackSportModeAddNewEvent(activityId)
            }
            .retryWhen { errors ->
                handleError(errors)
            }
            .subscribeOn(ioThread)
            .observeOn(mainThread)
            .doFinally { notifyChangesDone() }
    }

    override fun loadData() {
        notifyDataLoading()
        disposables.add(
            sportModesUseCase.fetchSportModes()
                .toFlowable()
                .flatMapIterable { it }
                // TODO remove it when we support multisport
                .filter { getStIdForMcId(it.id) != TRIATHLON_ID }
                .map {
                    SportModeCreateItem(it.id, onSelectSportModeItem)
                }
                .toSortedList(alphabeticalSort)
                .retryWhen { errors ->
                    handleError(errors)
                }
                .subscribeOn(ioThread)
                .observeOn(mainThread)
                .subscribe({ items ->
                    if (items.isEmpty()) {
                        notifyEmptyState()
                    } else {
                        notifyDataLoaded(items)
                    }
                }, {
                    Timber.w(it)
                    notifyEmptyState()
                })
        )
    }

    private fun trackSportModeAddNewEvent(activityId: Int) {
        val properties = defaultWatchProperties().put(
            AnalyticsEventProperty.ACTIVITY_TYPE,
            ActivityType.valueOf(getStIdForMcId(activityId)).simpleName
        )
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.SPORT_MODE_ADD_NEW, properties)
        emarsysAnalytics.trackEventWithProperties(AnalyticsEvent.SPORT_MODE_ADD_NEW, properties.map)
    }

    companion object {
        private const val TRIATHLON_ID = 74
    }
}
