package com.stt.android.watch.sportmodes.mappers

import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.stt.android.domain.sportmodes.Group
import com.stt.android.domain.sportmodes.Setting
import org.json.JSONArray
import org.json.JSONObject
import timber.log.Timber
import javax.inject.Inject

class SportModeJsonEditor
@Inject constructor(
    moshi: <PERSON><PERSON>
) {
    // Note: Use Moshi only when parsing the JSON to see if it was modified. Parsing unstructured
    // JSON like this results in all numbers being parsed as Double and formatted back incorrectly.
    private val jsonAdapter: JsonAdapter<Map<String, Any>> =
        moshi.adapter(Types.newParameterizedType(MutableMap::class.java, String::class.java, Any::class.java))

    fun getGroupWithUpdatedName(name: String, group: Group): Group {
        val value = group.value!!
        // Use JSONObject to modify JSON string in order to keep integers formatted as integers
        val json = JSONObject(value)
        json.getJSONObject(SETTINGS).getJSONArray(CUSTOM_MODE_GROUPS).getJSONObject(0).put(NAME, name)
        return group.copy(value = json.toMinifiedString())
    }

    fun getGroupNameAsString(group: Group): String {
        val value = group.value ?: return ""
        return try {
            val json = JSONObject(value)
            json.getJSONObject(SETTINGS).getJSONArray(CUSTOM_MODE_GROUPS).getJSONObject(0).getString(NAME)
        } catch (e: Exception) {
            Timber.w(e, "Failed to get sport mode name")
            ""
        }
    }

    fun getGroupWithExistingGroupIds(group: Group, existingGroupIds: List<Int>): Group {
        val value = group.value!!
        // Use JSONObject to modify JSON string in order to keep integers formatted as integers
        val json = JSONObject(value)
        json.getJSONObject(SETTINGS)
            .getJSONArray(CUSTOM_MODE_GROUPS)
            .getJSONObject(0)
            .put(CUSTOM_MODE_IDS, JSONArray(existingGroupIds))
        return group.copy(value = json.toMinifiedString())
    }

    @Suppress("UNCHECKED_CAST")
    fun getNameLengthLimits(setting: Setting): Pair<Int, Int> {
        return try {
            val meta = setting.meta as MutableMap<String, Any>
            val input = (meta[INPUTS] as List<MutableMap<String, Any>>).first()
            val length = input[LENGTH]
            val min = ((length as MutableMap<String, Any>)[MIN] as Double).toInt()
            val max = (length[MAX] as Double).toInt()
            min to max
        } catch (e: Exception) {
            Pair(0, 0)
        }
    }

    fun hasChanged(initialJson: String, currentJson: String): Boolean {
        val firstObject = jsonAdapter.fromJson(initialJson)
        val secondObject = jsonAdapter.fromJson(currentJson)
        val same = firstObject?.equals(secondObject) ?: true
        return !same
    }

    companion object {
        const val SETTINGS = "Settings"
        const val CUSTOM_MODE_GROUPS = "CustomModeGroups"
        const val CUSTOM_MODE_IDS = "CustomModeIDs"
        const val CUSTOM_MODES = "CustomModes"
        const val ACTIVITY_ID = "ActivityID"
        const val NAME = "Name"
        const val LENGTH = "length"
        const val INPUTS = "inputs"
        const val MIN = "min"
        const val MAX = "max"
    }
}

// Trivial minification as JSONObject has limited controls for output formatting
private fun JSONObject.toMinifiedString() =
    toString(0).replace("\n", "").replace("\": ", "\":")
