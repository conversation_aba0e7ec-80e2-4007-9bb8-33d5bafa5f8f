package com.stt.android.watch.sportmodes.list

import android.view.View
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.core.graphics.BlendModeColorFilterCompat
import androidx.core.graphics.BlendModeCompat
import androidx.core.view.isVisible
import androidx.navigation.findNavController
import com.stt.android.R
import com.stt.android.common.ui.ClickableItem
import com.stt.android.databinding.ItemSportmodeBinding
import com.stt.android.domain.sportmodes.SportModeHeader
import com.stt.android.domain.workout.ActivityType
import com.stt.android.home.diary.diarycalendar.activitygroups.ActivityTypeToGroupMapper
import com.stt.android.home.diary.diarycalendar.activitygroups.colorDrawableRes
import com.stt.android.infomodel.getStIdForMcId
import com.stt.android.core.R as CR

data class SportModeItem(
    val sportModeHeader: SportModeHeader,
    private val sportModeSelectedListener: OnSportModeSelectedListener,
    private val deleteInProgressDelegate: DeleteInProgressDelegate,
    private val isCustomMultisportMode: Boolean,
    val enabled: Boolean = true,
    val inDeletion: Boolean = false,
) : ClickableItem<ItemSportmodeBinding>(), View.OnLongClickListener {

    private val activityId = getStIdForMcId(sportModeHeader.activityHeader.id)

    private val activityType = ActivityType.valueOf(activityId)

    val title = activityType.localizedStringId

    val icon = activityType.iconId

    override fun onLongClick(v: View): Boolean {
        sportModeSelectedListener.changeInDeletion(!inDeletion)
        return true
    }

    override fun getId(): Long {
        return sportModeHeader.id.toLong()
    }

    override fun equals(other: Any?): Boolean {
        // Always return false or otherwise the UI is not updated when only the alpha value is changed
        return false
    }

    override fun getLayout() = R.layout.item_sportmode

    override fun onClick(view: View) {
        if (deleteInProgressDelegate.isDeletionInProcess() || inDeletion) {
            return
        }
        if (!sportModeHeader.factoryMode && enabled) {
            if (isCustomMultisportMode) {
                sportModeSelectedListener.onMultisportModeEditRequested(sportModeHeader)
            } else {
                val action = SportModeListFragmentDirections.sportModeEditDisplaysAction()
                action.sportModeId = sportModeHeader.modeIds[0]
                action.groupId = sportModeHeader.id
                action.activityName = view.context.getString(title)
                view.findNavController().navigate(action)
                sportModeSelectedListener.onSportModeItemClicked(this)
            }
        }
    }

    override fun bind(viewBinding: ItemSportmodeBinding, position: Int) {
        super.bind(viewBinding, position)
        viewBinding.root.isEnabled = !sportModeHeader.factoryMode && !inDeletion
        viewBinding.sportModeDeletionIcon.isVisible = inDeletion
        viewBinding.sportModeDeletionIcon.setOnClickListener {
            sportModeSelectedListener.deleteSportMode(this)
        }
        val context = viewBinding.root.context
        viewBinding.root.setBackgroundColor(ContextCompat.getColor(context, CR.color.white))
        viewBinding.sportModeIcon.setBackgroundResource(
            ActivityTypeToGroupMapper().activityTypeIdToGroup(
                activityType.id
            ).colorDrawableRes
        )
        viewBinding.sportModeIcon.setImageDrawable(AppCompatResources.getDrawable(context, icon))
        viewBinding.sportModeIcon.background?.colorFilter = null
        viewBinding.sportModeIcon.drawable?.colorFilter = null
        var alpha = 1f
        if (sportModeHeader.factoryMode && !inDeletion) {
            viewBinding.sportModeIcon.background
                .mutate()
                .colorFilter = BlendModeColorFilterCompat.createBlendModeColorFilterCompat(
                ContextCompat.getColor(context, CR.color.very_light_gray),
                BlendModeCompat.SRC_IN
            )
            viewBinding.sportModeIcon.drawable
                .mutate()
                .colorFilter = BlendModeColorFilterCompat.createBlendModeColorFilterCompat(
                ContextCompat.getColor(context, R.color.light_gray),
                BlendModeCompat.SRC_IN
            )
            alpha = 0.5f
        }
        viewBinding.sportModeItemSubheader.text = if (sportModeHeader.factoryMode) {
            sportModeHeader.activityHeader.type ?: context.getString(R.string.sport_mode_basic)
        } else {
            context.getString(R.string.sport_mode_custom, sportModeHeader.name)
        }
        viewBinding.sportModeItemHeader.alpha = alpha
        viewBinding.sportModeItemSubheader.alpha = alpha
    }
}
