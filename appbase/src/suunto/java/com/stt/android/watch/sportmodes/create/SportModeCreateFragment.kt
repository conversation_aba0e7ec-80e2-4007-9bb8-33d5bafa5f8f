package com.stt.android.watch.sportmodes.create

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.navigation.findNavController
import com.stt.android.R
import com.stt.android.common.ui.observeK
import com.stt.android.watch.sportmodes.SportModeFragment
import com.stt.android.watch.sportmodes.list.ToolbarDelegate
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class SportModeCreateFragment : SportModeFragment<SportModeCreateViewModel>() {
    override val viewModel: SportModeCreateViewModel by viewModels()

    @Inject
    lateinit var actionModeDelegate: ToolbarDelegate

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        viewModel.sharedViewModel.setTitle(getString(R.string.sport_modes_new_custom_mode_title))

        actionModeDelegate.changeToolbarToPickMode()
        viewModel.openSportModeEditDisplay.observeK(viewLifecycleOwner) { params ->
            params?.let {
                val action = SportModeCreateFragmentDirections.sportModeEditDisplaysAction().apply {
                    sportModeId = it.sportModeId
                    activityName = it.activityName
                }
                view?.findNavController()?.navigate(action)
            }
        }

        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        actionModeDelegate.changeToolbarToDefaultMode()
    }
}
