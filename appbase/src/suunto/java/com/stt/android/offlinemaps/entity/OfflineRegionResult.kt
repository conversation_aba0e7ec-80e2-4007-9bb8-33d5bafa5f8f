package com.stt.android.offlinemaps.entity

import androidx.compose.runtime.Immutable
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds
import com.stt.android.offlinemaps.datasource.DownloadOrder
import com.stt.android.offlinemaps.datasource.OfflineRegionStatus
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableMap
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf

sealed interface OfflineRegionResult : ResultId {
    @Immutable
    data class OfflineRegion(
        override val id: String,
        val name: String,
        val sizes: ImmutableMap<OfflineMapDownloadTarget, Size>,
        val area: Double?,
        val areaUnitRes: Int?,
        val description: String?,
        val bounds: LatLngBounds?,
        val boundaryUrl: String?,
        val maskUrl: String?,
        val downloadOrder: DownloadOrder?,
        val styleIds: ImmutableList<String>,
        val groupName: String? = null,
        val adjacentRegions: ImmutableList<String> = persistentListOf(),
        val adjacentMaskUrl: String? = null,
        val centerPoint: LatLng? = null,
        val batchDownloadAllowed: Boolean? = null
    ) : OfflineRegionResult {
        data class Size(
            val storageSizeInBytes: Long?,
            val transferSizeInBytes: Long?,
        )

        val sizeForWatch: Size? get() = sizes[OfflineMapDownloadTarget.WATCH]

        val sizeForMobile: Size? get() = sizes[OfflineMapDownloadTarget.MOBILE]

        val downloadRequested: Boolean get() = downloadOrder?.status == OfflineRegionStatus.REQUESTED

        val downloading: Boolean get() = downloadOrder?.status == OfflineRegionStatus.IN_PROGRESS ||
            downloadOrder?.status == OfflineRegionStatus.UPDATE_IN_PROGRESS

        val downloadingUpdate: Boolean get() = downloadOrder?.status == OfflineRegionStatus.UPDATE_IN_PROGRESS

        val downloaded: Boolean get() = downloadOrder?.status == OfflineRegionStatus.FINISHED ||
            downloadOrder?.status == OfflineRegionStatus.UPDATE_AVAILABLE ||
            downloadOrder?.status == OfflineRegionStatus.UPDATE_IN_PROGRESS

        val downloadFailed: Boolean get() = downloadOrder?.status == OfflineRegionStatus.FAILED

        val deleteRequested: Boolean get() = downloadOrder?.status == OfflineRegionStatus.DELETE_REQUESTED

        val downloadAvailable: Boolean get() = when (downloadOrder?.status) {
            OfflineRegionStatus.DELETE_FINISHED,
            OfflineRegionStatus.FAILED,
            null -> true
            else -> false
        }

        val updateAvailable: Boolean get() = downloadOrder?.status == OfflineRegionStatus.UPDATE_AVAILABLE

        val cancellable: Boolean get() = downloadRequested || (downloading && !downloadingUpdate) || deleteRequested

        val downloadProgress: Float get() {
            val downloaded = downloadOrder?.downloadedSize
            val transferSize = sizeForWatch?.transferSizeInBytes
            return if (downloaded == null || transferSize == null || transferSize == 0L) {
                0f
            } else {
                (downloaded / transferSize.toFloat()).coerceAtMost(1f)
            }
        }

        companion object {
            val EMPTY = OfflineRegion(
                id = "_empty_region_",
                name = "",
                sizes = persistentMapOf(),
                area = null,
                areaUnitRes = null,
                description = null,
                bounds = null,
                boundaryUrl = null,
                maskUrl = null,
                styleIds = persistentListOf(),
                downloadOrder = null,
            )
        }
    }

    @Immutable
    data class OfflineRegionGroup(
        override val id: String,
        val name: String,
        val size: Long,
        val regions: ImmutableList<OfflineRegion>,
        val batchDownloadAllowed: Boolean? = null
    ) : OfflineRegionResult {
        companion object {
            val EMPTY = OfflineRegionGroup(
                id = "_empty_region_group_",
                name = "",
                size = 0,
                regions = persistentListOf()
            )
        }
    }
}

interface ResultId {
    val id: String
}
