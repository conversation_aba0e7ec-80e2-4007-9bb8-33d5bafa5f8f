package com.stt.android.offlinemaps.ui

import android.text.format.Formatter.formatShortFileSize
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.component.SuuntoLinearProgressBar
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.header
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.highlightedString
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.offlinemaps.datasource.DownloadOrder
import com.stt.android.offlinemaps.datasource.DummyOfflineRegionDataSource
import com.stt.android.offlinemaps.datasource.OfflineRegionStatus
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import java.util.Locale

@Composable
fun OfflineRegionItem(
    region: OfflineRegionResult.OfflineRegion,
    cancellingDownload: Boolean,
    requestingDownload: Boolean,
    onClick: () -> Unit,
    onCancel: (OfflineRegionResult.OfflineRegion) -> Unit,
    onRetry: (OfflineRegionResult.OfflineRegion) -> Unit,
    modifier: Modifier = Modifier,
    highlight: String? = null,
    onViewInLibrary: ((OfflineRegionResult.OfflineRegion) -> Unit)? = null,
) {
    var showCancelConfirmation by rememberSaveable { mutableStateOf(false) }

    val secondaryText = when {
        region.downloadRequested -> stringResource(id = R.string.download_requested)
        region.downloading -> stringResource(id = R.string.download_downloading)
        region.downloaded && onViewInLibrary != null -> stringResource(id = R.string.download_downloaded)
        region.downloadFailed -> stringResource(id = R.string.download_failed)
        else -> regionSecondaryText(region = region)
    }

    val secondaryTextColor: Color = when {
        region.downloadFailed -> MaterialTheme.colorScheme.error
        region.downloaded && onViewInLibrary != null -> Color(0x1A, 0xA2, 0x43)
        else -> MaterialTheme.colorScheme.onSurfaceVariant
    }

    val action = getRegionAction(
        region = region,
        cancellingDownload = cancellingDownload,
        requestingDownload = requestingDownload,
        onViewInLibrary = onViewInLibrary,
        onCancel = { showCancelConfirmation = true },
        onRetry = onRetry
    )

    Column(
        modifier = modifier
            .background(color = MaterialTheme.colorScheme.surface)
            .then(if (action == null) Modifier.clickable(onClick = onClick) else Modifier)
            .padding(MaterialTheme.spacing.medium),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painterResource(R.drawable.map_type_outline),
                contentDescription = null,
                modifier = Modifier
                    .size(MaterialTheme.iconSizes.medium),
                tint = MaterialTheme.colorScheme.secondary,
            )

            Spacer(Modifier.width(MaterialTheme.spacing.medium))

            Column {
                Text(
                    highlightedString(
                        text = region.name,
                        highlight = highlight,
                        highlightStyle = SpanStyle(
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Bold,
                        ),
                    ),
                    style = MaterialTheme.typography.bodyLarge,
                )

                Text(
                    text = highlightedString(
                        text = secondaryText,
                        highlight = highlight,
                        highlightStyle = SpanStyle(
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Bold,
                        )
                    ),
                    color = secondaryTextColor,
                    style = MaterialTheme.typography.bodyMedium,
                )
            }

            Spacer(Modifier.weight(1.0F))

            if (action == null) {
                Icon(
                    painter = SuuntoIcons.ActionRight.asPainter(),
                    contentDescription = null,
                    modifier = Modifier.size(MaterialTheme.iconSizes.medium)
                )
            } else {
                action()
            }
        }

        SuuntoLinearProgressBar(
            progress = region.downloadProgress,
            modifier = Modifier
                .alpha(if (region.downloading) 1f else 0f)
                .fillMaxWidth(),
        )
    }

    if (showCancelConfirmation) {
        ConfirmationDialog(
            title = stringResource(
                if (region.deleteRequested) {
                    R.string.cancel_delete_request_title
                } else {
                    R.string.cancel_download_confirmation_title
                }
            ),
            text = stringResource(
                if (region.deleteRequested) {
                    R.string.cancel_delete_request_text
                } else {
                    R.string.cancel_download_confirmation_text
                }
            ),
            cancelButtonText = stringResource(R.string.no),
            confirmButtonText = stringResource(R.string.yes),
            onDismissRequest = { showCancelConfirmation = false },
            onConfirm = {
                showCancelConfirmation = false
                onCancel(region)
            }
        )
    }
}

private inline fun getRegionAction(
    region: OfflineRegionResult.OfflineRegion,
    cancellingDownload: Boolean,
    requestingDownload: Boolean,
    noinline onViewInLibrary: ((OfflineRegionResult.OfflineRegion) -> Unit)?,
    crossinline onCancel: (OfflineRegionResult.OfflineRegion) -> Unit,
    crossinline onRetry: (OfflineRegionResult.OfflineRegion) -> Unit
): @Composable (() -> Unit)? = when {
    onViewInLibrary != null && (region.downloaded || region.deleteRequested) -> {
        {
            TextButton(
                onClick = { onViewInLibrary(region) },
                modifier = Modifier.widthIn(max = 140.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.view_in_library).uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.header,
                )
            }
        }
    }
    onViewInLibrary == null && (region.updateAvailable || region.downloadingUpdate) -> {
        {}
    }
    region.cancellable -> {
        {
            TextButton(onClick = { onCancel(region) }, enabled = !cancellingDownload) {
                Text(
                    text = stringResource(id = R.string.cancel).uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.header,
                )
            }
        }
    }
    region.downloadFailed -> {
        {
            TextButton(onClick = { onRetry(region) }, enabled = !requestingDownload) {
                Text(
                    text = stringResource(id = R.string.retry).uppercase(Locale.getDefault()),
                    style = MaterialTheme.typography.header,
                )
            }
        }
    }
    else -> null
}

@Composable
private fun regionSecondaryText(region: OfflineRegionResult.OfflineRegion): String {
    val size = region.sizeForWatch?.storageSizeInBytes ?: return ""
    val formattedSize = formatShortFileSize(LocalContext.current, size)
    return if (region.groupName.isNullOrBlank()) {
        formattedSize
    } else {
        "$formattedSize · ${region.groupName}"
    }
}

@Preview(showBackground = true)
@Composable
private fun OfflineRegionItemPreview(
    @PreviewParameter(OfflineRegionItemParamsProvider::class) params: OfflineRegionItemParams
) {
    M3AppTheme {
        OfflineRegionItem(
            region = params.region,
            cancellingDownload = false,
            requestingDownload = false,
            onClick = {},
            onCancel = {},
            onRetry = {},
            onViewInLibrary = {},
        )
    }
}

private class OfflineRegionItemParamsProvider : PreviewParameterProvider<OfflineRegionItemParams> {
    override val values: Sequence<OfflineRegionItemParams>
        get() {
            val dummyRegion = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS
                .first { it.name == "Finland" }.regions.first()
            return sequenceOf(
                OfflineRegionItemParams(region = dummyRegion.copy(downloadOrder = null)),
                OfflineRegionItemParams(
                    region = dummyRegion.copy(
                        downloadOrder = DownloadOrder(
                            "",
                            null,
                            status = OfflineRegionStatus.REQUESTED,
                            downloadedSize = 0,
                            sourceTypeUsed = "TILE_LIST",
                        )
                    ),
                    action = {
                        TextButton(onClick = { }) {
                            Text(text = stringResource(id = R.string.cancel))
                        }
                    }
                ),
                OfflineRegionItemParams(
                    region = dummyRegion
                        .copy(
                            name = "A very long name that will not fit the space available on one line",
                            downloadOrder = DownloadOrder(
                                "",
                                null,
                                status = OfflineRegionStatus.UPDATE_IN_PROGRESS,
                                downloadedSize = 0,
                                sourceTypeUsed = "TILE_LIST",
                            )
                        ),
                    action = {
                        TextButton(onClick = { }) {
                            Text(text = stringResource(id = R.string.cancel))
                        }
                    }
                ),
                OfflineRegionItemParams(
                    region = dummyRegion
                        .copy(
                            name = "A very long name that will not fit the space available on one line or even on two lines and needs a third",
                            downloadOrder = DownloadOrder(
                                "",
                                null,
                                status = OfflineRegionStatus.FINISHED,
                                downloadedSize = 0,
                                sourceTypeUsed = "TILE_LIST",
                            )
                        ),
                    action = {
                        TextButton(onClick = { }) {
                            Text(text = stringResource(id = R.string.view_in_library))
                        }
                    }
                ),
                OfflineRegionItemParams(
                    region = dummyRegion
                        .copy(
                            downloadOrder = DownloadOrder(
                                "",
                                null,
                                status = OfflineRegionStatus.FAILED,
                                downloadedSize = 0,
                                sourceTypeUsed = "TILE_LIST",
                            )
                        ),
                    action = {
                        TextButton(onClick = { }) {
                            Text(text = stringResource(id = R.string.view_in_library))
                        }
                    }
                ),
            )
        }
}

private data class OfflineRegionItemParams(
    val region: OfflineRegionResult.OfflineRegion,
    val action: @Composable (() -> Unit)? = null
)
