package com.stt.android.offlinemaps.ui

import android.app.Activity
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.component.SuuntoModalBottomSheet
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.compose.widgets.InfoDialog
import com.stt.android.compose.widgets.SearchBar
import com.stt.android.offlinemaps.OfflineMapsSelectionEventHandler
import com.stt.android.offlinemaps.OfflineMapsSelectionViewEvent
import com.stt.android.offlinemaps.OfflineMapsSelectionViewState
import com.stt.android.offlinemaps.datasource.DummyOfflineRegionDataSource
import com.stt.android.offlinemaps.entity.FreeSpaceAvailable
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.search.OfflineMapsSearchEventHandler
import com.stt.android.offlinemaps.search.OfflineMapsSearchViewEvent
import com.stt.android.offlinemaps.search.OfflineMapsSearchViewState
import com.stt.android.offlinemaps.watchstatus.entities.WatchDownloadStatus
import com.stt.android.offlinemaps.watchstatus.ui.WatchDownloadStatus
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentSetOf

@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun OfflineRegionsScreen(
    searchViewState: OfflineMapsSearchViewState,
    searchEventHandler: OfflineMapsSearchEventHandler,
    offlineMapsSelectionViewState: OfflineMapsSelectionViewState,
    offlineMapsSelectionEventHandler: OfflineMapsSelectionEventHandler,
    listData: OfflineRegionListData,
    watchDownloadStatus: WatchDownloadStatus,
    onViewInLibrary: (OfflineRegionResult.OfflineRegion?) -> Unit,
    onEnableWifi: () -> Unit,
    onWifiSetup: () -> Unit,
    onDownloadInfoLink: (LinkTarget) -> Unit,
    navigateUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    var inSearchMode by rememberSaveable { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    val bottomSheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true,
    )

    val title = when (listData) {
        is OfflineRegionListData.Group -> listData.group.name
        is OfflineRegionListData.Catalogue -> stringResource(R.string.offline_maps_download_maps_button)
    }
    val results = when (listData) {
        is OfflineRegionListData.Group -> listData.group.regions
        is OfflineRegionListData.Catalogue -> listData.groups
    }

    val nearbyResults = when (listData) {
        is OfflineRegionListData.Group -> persistentListOf()
        is OfflineRegionListData.Catalogue -> listData.nearby
    }

    DisposableEffectOnLifecycleStart {
        if (searchViewState.searchTerm.isNotBlank()) {
            searchEventHandler(OfflineMapsSearchViewEvent.UpdateSearchTerm(searchViewState.searchTerm))
        }
    }

    Scaffold(
        modifier = modifier,
        topBar = {
            OfflineRegionsTopBar(
                title = title,
                navigateUp = navigateUp,
                showBottomSheet = { offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.ShowDownloadInfo) },
            )
        }
    ) { contentPadding ->
        BackHandler {
            if (bottomSheetState.isVisible) {
                offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.DismissDownloadInfo)
            } else {
                navigateUp()
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .narrowContent()
                .padding(contentPadding)
                .background(MaterialTheme.colorScheme.surface),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        ) {
            WatchDownloadStatus(
                status = watchDownloadStatus,
                modifier = Modifier.fillMaxWidth(),
            )

            when (offlineMapsSelectionViewState) {
                OfflineMapsSelectionViewState.Loading -> Box(
                    modifier = Modifier.fillMaxSize(),
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center),
                    )
                }
                is OfflineMapsSelectionViewState.Loaded -> {
                    OfflineRegionsSearchBar(
                        searchViewState = searchViewState,
                        searchEventHandler = searchEventHandler,
                        offlineMapsSelectionEventHandler = offlineMapsSelectionEventHandler,
                        inSearchMode = inSearchMode,
                        onInSearchModeChange = { inSearchMode = it },
                        focusRequester = focusRequester,
                        focusManager = focusManager,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = MaterialTheme.spacing.medium),
                    )

                    if (inSearchMode) {
                        SearchResults(
                            viewState = searchViewState,
                            offlineMapsSelectionViewState = offlineMapsSelectionViewState,
                            offlineMapsSelectionEventHandler = offlineMapsSelectionEventHandler,
                            onViewInLibrary = onViewInLibrary,
                            modifier = Modifier.fillMaxSize(),
                        )
                    } else {
                        OfflineRegionList(
                            offlineRegions = results,
                            nearbyOfflineRegions = nearbyResults,
                            showDownloadsSection = listData is OfflineRegionListData.Catalogue,
                            offlineMapsSelectionViewState = offlineMapsSelectionViewState,
                            offlineMapsSelectionEventHandler = offlineMapsSelectionEventHandler,
                            onViewInLibrary = onViewInLibrary,
                            modifier = Modifier.fillMaxSize(),
                        )
                    }
                }
            }
        }
    }

    when (offlineMapsSelectionViewState) {
        OfflineMapsSelectionViewState.Loading -> Unit
        is OfflineMapsSelectionViewState.Loaded -> {
            LaunchedEffect(offlineMapsSelectionViewState.catalogue) {
                searchEventHandler(OfflineMapsSearchViewEvent.UpdateCatalogue(offlineMapsSelectionViewState.catalogue))
            }

            if (offlineMapsSelectionViewState.showDownloadInfo) {
                SuuntoModalBottomSheet(
                    onDismissRequest = { offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.DismissDownloadInfo) },
                    sheetState = bottomSheetState,
                ) {
                    DownloadInfoSheet(onDownloadInfoLink = onDownloadInfoLink)
                }

                LaunchedEffect(key1 = Unit) {
                    bottomSheetState.expand()
                }
            } else {
                LaunchedEffect(key1 = Unit) {
                    bottomSheetState.hide()
                }
            }

            if (offlineMapsSelectionViewState.downloadError != null) {
                val title = stringResource(
                    if (offlineMapsSelectionViewState.downloadError.description != null) {
                        R.string.download_request_failed_generic_text
                    } else {
                        R.string.download_request_failed_title
                    }
                )
                val text = offlineMapsSelectionViewState.downloadError.description
                    ?: stringResource(
                        R.string.download_request_failed_text,
                        offlineMapsSelectionViewState.downloadError.regionName,
                    )

                InfoDialog(
                    title = title,
                    text = text,
                    confirmButtonText = stringResource(R.string.ok),
                    onDismissRequest = { offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.MarkDownloadErrorAsShown) },
                    onConfirm = { offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.MarkDownloadErrorAsShown) }
                )
            }

            val downloadQueueCount = when (listData) {
                is OfflineRegionListData.Group -> listData.group.regions.count { it.downloadRequested }
                is OfflineRegionListData.Catalogue -> listData.groups.flatMap { it.regions }.count { it.downloadRequested }
            }

            val showBatteryTipDialog = downloadQueueCount > 0 &&
                offlineMapsSelectionViewState.showBatteryInfoWithPendingDownload
            FullscreenAnimationPopup(
                visible = showBatteryTipDialog,
                onBatteryTipDismiss = { offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.DismissBatteryInfo) },
            )

            val showSetupWifiDialog = downloadQueueCount > 0 &&
                offlineMapsSelectionViewState.showWifiSetupInfoWithPendingDownload
            if (showSetupWifiDialog) {
                ConfirmationDialog(
                    title = stringResource(R.string.setup_wifi_query_title),
                    text = stringResource(R.string.setup_wifi_query_text),
                    cancelButtonText = stringResource(R.string.not_now),
                    confirmButtonText = stringResource(R.string.setup_wifi_now),
                    onDismissRequest = { offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.DismissWifiSetupInfo) },
                    onConfirm = {
                        offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.DismissWifiSetupInfo)
                        onWifiSetup()
                    }
                )
            }

            val showWifiDisabledDialog = downloadQueueCount > 0 &&
                offlineMapsSelectionViewState.showWifiDisabledInfoWithPendingDownload
            if (showWifiDisabledDialog) {
                ConfirmationDialog(
                    title = stringResource(R.string.wifi_disabled_title),
                    text = stringResource(R.string.wifi_disabled_text),
                    cancelButtonText = stringResource(R.string.not_now),
                    confirmButtonText = stringResource(R.string.enable),
                    onDismissRequest = { offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.DismissWifiDisabledInfo) },
                    onConfirm = {
                        offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.DismissWifiDisabledInfo)
                        onEnableWifi()
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun OfflineRegionsTopBar(
    title: String,
    navigateUp: () -> Unit,
    showBottomSheet: () -> Unit,
    modifier: Modifier = Modifier,
) {
    SuuntoTopBar(
        title = title,
        onNavigationClick = navigateUp,
        modifier = modifier,
        actions = {
            SuuntoIconButton(
                icon = SuuntoIcons.Info,
                onClick = showBottomSheet,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
            )
        },
    )
}

@Composable
private fun OfflineRegionsSearchBar(
    searchViewState: OfflineMapsSearchViewState,
    searchEventHandler: OfflineMapsSearchEventHandler,
    offlineMapsSelectionEventHandler: OfflineMapsSelectionEventHandler,
    inSearchMode: Boolean,
    onInSearchModeChange: (Boolean) -> Unit,
    focusRequester: FocusRequester,
    focusManager: FocusManager,
    modifier: Modifier = Modifier,
) {
    SearchBar(
        searching = searchViewState is OfflineMapsSearchViewState.Searching,
        searchTerm = searchViewState.searchTerm,
        onSearchTermChange = { searchTerm -> searchEventHandler(OfflineMapsSearchViewEvent.UpdateSearchTerm(searchTerm)) },
        onResetSearchTerm = { searchEventHandler(OfflineMapsSearchViewEvent.ResetSearchTerm) },
        cancelButtonText = stringResource(id = R.string.cancel),
        showCancelButton = inSearchMode,
        onCancel = {
            focusManager.clearFocus()
            onInSearchModeChange(false)
            searchEventHandler(OfflineMapsSearchViewEvent.ResetSearchTerm)
        },
        modifier = modifier
            .onFocusChanged {
                if (it.hasFocus) {
                    if (!inSearchMode) {
                        offlineMapsSelectionEventHandler(OfflineMapsSelectionViewEvent.TrackSearchModeEnabled)
                    }
                    onInSearchModeChange(true)
                }
            },
        placeholder = stringResource(R.string.search_bar_placeholder_by_name),
        focusRequester = focusRequester
    )
}

@Composable
private fun DisposableEffectOnLifecycleStart(
    effect: () -> Unit,
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    val context = LocalContext.current

    var isChangingConfigurations by rememberSaveable { mutableStateOf(false) }

    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_DESTROY -> {
                    if (context is Activity && context.isChangingConfigurations) {
                        isChangingConfigurations = true
                    }
                }

                Lifecycle.Event.ON_START -> {
                    if (!isChangingConfigurations) {
                        effect()
                    }
                    isChangingConfigurations = false
                }

                else -> Unit
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}

@Preview
@Composable
private fun OfflineRegionsScreenPreview(
    @PreviewParameter(OfflineRegionsScreenParamsProvider::class) offlineRegionListData: OfflineRegionListData,
) {
    M3AppTheme {
        OfflineRegionsScreen(
            searchViewState = OfflineMapsSearchViewState.NoSearchTerm,
            searchEventHandler = {},
            offlineMapsSelectionViewState = OfflineMapsSelectionViewState.Loaded(
                supportsOfflineMapsOnMobile = true,
                selectedDownloadTargets = persistentSetOf(),
                showBatteryInfoWithPendingDownload = false,
                showWifiDisabledInfoWithPendingDownload = false,
                showWifiSetupInfoWithPendingDownload = false,
                catalogue = OfflineRegionListData.Catalogue(
                    nearby = persistentListOf(),
                    groups = persistentListOf(),
                ),
                selectedRegion = null,
                selectedRegionGroup = null,
                showDownloadInfo = false,
                downloadError = null,
                cancellingDownload = false,
                requestingDownload = false,
                updateAvailableCount = 6,
                freeSpaceAvailable = FreeSpaceAvailable(6000000L, 10),
            ),
            offlineMapsSelectionEventHandler = {},
            listData = offlineRegionListData,
            watchDownloadStatus = WatchDownloadStatus.NOT_CHARGING,
            onViewInLibrary = {},
            onEnableWifi = {},
            onWifiSetup = {},
            onDownloadInfoLink = {},
            navigateUp = {},
        )
    }
}

private class OfflineRegionsScreenParamsProvider :
    PreviewParameterProvider<OfflineRegionListData> {
    override val values: Sequence<OfflineRegionListData> = sequenceOf(
        OfflineRegionListData.Group(
            DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS
                .first { it.name == "Finland" }
        ),
        OfflineRegionListData.Catalogue(
            groups = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS
        ),
    )
}
