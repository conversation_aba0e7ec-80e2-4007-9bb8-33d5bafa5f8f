package com.stt.android.offlinemaps.ui

import androidx.annotation.StringRes
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.offlinemaps.OfflineMapsSelectionEventHandler
import com.stt.android.offlinemaps.OfflineMapsSelectionViewState
import com.stt.android.offlinemaps.datasource.DummyOfflineRegionDataSource
import com.stt.android.offlinemaps.entity.OfflineRegionListData
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.offlinemaps.search.OfflineMapsSearchViewState
import kotlinx.collections.immutable.persistentSetOf
import kotlinx.collections.immutable.toPersistentList

@Composable
internal fun SearchResults(
    viewState: OfflineMapsSearchViewState,
    offlineMapsSelectionViewState: OfflineMapsSelectionViewState.Loaded,
    offlineMapsSelectionEventHandler: OfflineMapsSelectionEventHandler,
    onViewInLibrary: (OfflineRegionResult.OfflineRegion?) -> Unit,
    modifier: Modifier = Modifier,
) {
    when (viewState) {
        is OfflineMapsSearchViewState.NoSearchTerm -> InfoState(
            text = R.string.search_info_text,
            modifier = modifier,
        )
        is OfflineMapsSearchViewState.Searching -> Unit
        is OfflineMapsSearchViewState.NoMatchFound -> InfoState(
            text = R.string.search_no_results_text,
            modifier = modifier,
        )
        is OfflineMapsSearchViewState.MatchesFound -> {
            LazyColumn(
                modifier = modifier.fillMaxSize()
            ) {
                itemsIndexed(
                    items = viewState.searchResult,
                    key = { _, region -> region.id },
                ) { index, region ->
                    RegionListItem(
                        offlineRegion = region,
                        offlineMapsSelectionViewState = offlineMapsSelectionViewState,
                        offlineMapsSelectionEventHandler = offlineMapsSelectionEventHandler,
                        onViewInLibrary = onViewInLibrary,
                        highlight = viewState.searchTerm,
                        showDivider = index > 0,
                    )
                }
            }
        }
    }
}

@Composable
private fun InfoState(
    @StringRes text: Int,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(Modifier.height(MaterialTheme.spacing.xxxxlarge))

        Icon(
            painter = SuuntoIcons.ActionSearch.asPainter(),
            contentDescription = null,
            modifier = Modifier
                .size(MaterialTheme.iconSizes.large),
            tint = MaterialTheme.colorScheme.secondary,
        )

        Spacer(Modifier.height(MaterialTheme.spacing.medium))

        Text(
            text = stringResource(text),
            modifier = Modifier
                .padding(horizontal = MaterialTheme.spacing.xxlarge),
            textAlign = TextAlign.Center,
            color = MaterialTheme.colorScheme.secondary,
        )
    }
}

@Preview(showBackground = true, widthDp = 360, heightDp = 720)
@Preview(showBackground = true, widthDp = 720, heightDp = 360)
@Composable
private fun SearchResultsPreview(
    @PreviewParameter(SearchViewStateProvider::class) viewState: OfflineMapsSearchViewState,
) {
    M3AppTheme {
        SearchResults(
            viewState = viewState,
            offlineMapsSelectionViewState = OfflineMapsSelectionViewState.Loaded(
                supportsOfflineMapsOnMobile = true,
                selectedDownloadTargets = persistentSetOf(),
                showBatteryInfoWithPendingDownload = false,
                showWifiDisabledInfoWithPendingDownload = false,
                showWifiSetupInfoWithPendingDownload = false,
                catalogue = OfflineRegionListData.Catalogue(),
                selectedRegion = null,
                selectedRegionGroup = null,
                showDownloadInfo = false,
                downloadError = null,
                cancellingDownload = false,
                requestingDownload = false,
                updateAvailableCount = 0,
                freeSpaceAvailable = null,
            ),
            offlineMapsSelectionEventHandler = {},
            onViewInLibrary = {},
        )
    }
}

private class SearchViewStateProvider : PreviewParameterProvider<OfflineMapsSearchViewState> {
    override val values: Sequence<OfflineMapsSearchViewState> = sequenceOf(
        OfflineMapsSearchViewState.NoSearchTerm,
        OfflineMapsSearchViewState.NoMatchFound(
            searchTerm = "keyword",
        ),
        OfflineMapsSearchViewState.MatchesFound(
            searchTerm = "keyword",
            searchResult = (DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS + DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS)
                    .first { it.name == "Finland" }
                    .regions.toPersistentList(),
        ),
    )
}
