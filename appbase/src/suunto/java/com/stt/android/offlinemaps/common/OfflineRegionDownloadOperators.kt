package com.stt.android.offlinemaps.common

import androidx.annotation.Size
import com.stt.android.coroutines.AppCoroutineScopeProvider
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.offlinemaps.datasource.OfflineRegionDownloadRepository
import com.stt.android.offlinemaps.entity.OfflineMapDownloadTarget
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.watch.offlinemaps.domain.NotifyAreaSelectionChangedUseCase
import com.stt.android.watch.offlinemaps.domain.NotifyAreaUnderDownloadDeletedUseCase
import com.suunto.connectivity.repository.SuuntoRepositoryException
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

interface OfflineRegionDownloadOperators {
    suspend fun downloadOfflineRegion(
        @Size(min = 1L) downloadTo: Set<OfflineMapDownloadTarget>,
        regionId: String,
        groupName: String? = null,
    ): OfflineRegionResult.OfflineRegion

    suspend fun deleteDownload(
        region: OfflineRegionResult.OfflineRegion,
        deleteFrom: OfflineMapDownloadTarget,
    ): OfflineRegionResult.OfflineRegion

    suspend fun getMapStorageSize(): Long?
}

class OfflineRegionDownloadOperatorsDelegate @Inject constructor(
    private val notifyAreaSelectionChangedUseCase: NotifyAreaSelectionChangedUseCase,
    private val notifyAreaUnderDownloadDeletedUseCase: NotifyAreaUnderDownloadDeletedUseCase,
    private val repository: OfflineRegionDownloadRepository,
    private val appCoroutineScopeProvider: AppCoroutineScopeProvider,
) : OfflineRegionDownloadOperators {
    override suspend fun downloadOfflineRegion(
        downloadTo: Set<OfflineMapDownloadTarget>,
        regionId: String,
        groupName: String?,
    ): OfflineRegionResult.OfflineRegion {
        val offlineRegion = repository.downloadOfflineRegion(
            regionId = regionId,
            groupName = groupName,
        )

        if (downloadTo.contains(OfflineMapDownloadTarget.WATCH)) {
            notifyWatch()
        }

        // TODO 187306 Support download region to mobile

        return offlineRegion
    }

    override suspend fun deleteDownload(
        region: OfflineRegionResult.OfflineRegion,
        deleteFrom: OfflineMapDownloadTarget,
    ): OfflineRegionResult.OfflineRegion {
        val offlineRegion = repository.deleteDownload(
            region = region,
        )

        when (deleteFrom) {
            OfflineMapDownloadTarget.MOBILE -> Unit // TODO 187306 Support delete region from mobile
            OfflineMapDownloadTarget.WATCH -> notifyWatch(
                regionId = region.id,
                ongoingDownloadCancelled = region.downloading,
            )
        }

        return offlineRegion
    }

    override suspend fun getMapStorageSize(): Long? = repository.getMapStorageSize()

    private fun notifyWatch(
        regionId: String? = null,
        ongoingDownloadCancelled: Boolean = false,
    ) {
        // Breaking structured concurrency letting the download operations and app execution to
        // continue. We should always try to notify the watch, but the operation is not critical and
        // the UI should not wait for these to complete.
        appCoroutineScopeProvider.appCoroutineScope.launch {
            if (ongoingDownloadCancelled && regionId != null) {
                runSuspendCatching {
                    notifyAreaUnderDownloadDeletedUseCase(regionId)
                }.onSuccess {
                    Timber.d("AreaUnderDownloadDeleted sent to watch")
                }.onFailure { e ->
                    if (e is SuuntoRepositoryException && e.message?.contains("Failed status: 404") == true) {
                        Timber.d("Failed to send AreaUnderDownloadDeleted to the watch. API not supported or watch not connected.")
                    } else {
                        Timber.w(e, "Failed to send AreaUnderDownloadDeleted to the watch")
                    }
                }
            }

            runSuspendCatching {
                notifyAreaSelectionChangedUseCase()
            }.onSuccess {
                Timber.d("AreaSelectionChanged sent to watch")
            }.onFailure { e ->
                if (e is SuuntoRepositoryException && e.message?.contains("Failed status: 404") == true) {
                    Timber.d("Failed to send AreaSelectionChanged to the watch. Watch is not connected.")
                } else {
                    Timber.w(e, "Failed to send AreaSelectionChanged to the watch")
                }
            }
        }
    }
}
