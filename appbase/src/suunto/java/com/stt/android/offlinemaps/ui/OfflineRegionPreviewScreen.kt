package com.stt.android.offlinemaps.ui

import android.annotation.SuppressLint
import android.app.Activity.RESULT_CANCELED
import android.content.Context
import android.content.res.Resources
import android.text.format.Formatter
import android.view.Gravity
import androidx.activity.compose.BackHandler
import androidx.annotation.StringRes
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SheetValue
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.rememberBottomSheetScaffoldState
import androidx.compose.material3.rememberStandardBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import com.google.android.gms.maps.model.LatLng
import com.stt.android.R
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.component.SuuntoSwitch
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.layout.CenteringBottomSheetScaffold
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcon
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.darkGrey
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodySmallBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.material3.numbers2
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.InfoDialog
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.maps.SuuntoBitmapResourceDescriptor
import com.stt.android.maps.SuuntoCameraUpdateNewLatLngBounds
import com.stt.android.maps.SuuntoChinaOfflineRegion
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.mapbox.MapboxMapsProvider
import com.stt.android.offlinemaps.datasource.DummyOfflineRegionDataSource
import com.stt.android.offlinemaps.entity.OfflineMapDownloadTarget
import com.stt.android.offlinemaps.entity.OfflineMapRegionAndSelectState
import com.stt.android.offlinemaps.entity.OfflineRegionResult
import com.stt.android.ui.map.MapHelper
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableSet
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentSetOf
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OfflineRegionPreviewScreen(
    region: OfflineRegionResult.OfflineRegion,
    supportsOfflineMapsOnMobile: Boolean,
    selectedDownloadTargets: ImmutableSet<OfflineMapDownloadTarget>,
    onDownloadTargetSelectionChanged: (OfflineMapDownloadTarget, Boolean) -> Unit,
    onDownloadRegion: (OfflineRegionResult.OfflineRegion) -> Unit,
    onOsmDisclaimer: () -> Unit,
    onDeleteRegion: (OfflineMapDownloadTarget) -> Unit,
    navigateUp: (Int) -> Unit,
    modifier: Modifier = Modifier,
    onSelectRegion: (String) -> Unit = {},
    allChinaRegionsAndSelectState: ImmutableList<OfflineMapRegionAndSelectState> = persistentListOf(),
    selectedChinaRegions: ImmutableList<OfflineRegionResult.OfflineRegion> = persistentListOf(),
    onLoadAllChinaOfflineRegionSuccess: (List<SuuntoChinaOfflineRegion>, List<SuuntoMarker>) -> Unit = { _: List<SuuntoChinaOfflineRegion>, _: List<SuuntoMarker> -> },
    watchStorageFull: Boolean = false,
    onWatchStorageFullDialogDismissRequest: () -> Unit = {},
    onMapScaleListener: (Float) -> Unit = {}
) {
    val context = LocalContext.current
    var sheetHeight by rememberSaveable { mutableIntStateOf(0) }
    var mapHeight by rememberSaveable { mutableIntStateOf(0) }
    val bottomSheetState = rememberStandardBottomSheetState(
        initialValue = SheetValue.PartiallyExpanded,
    )
    val mapPadding = with(LocalDensity.current) { MaterialTheme.spacing.small.toPx().toInt() }
    var menuExpanded by rememberSaveable { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()
    var showSelectRegionInfo by rememberSaveable { mutableStateOf(false) }

    BackHandler {
        if (showSelectRegionInfo) {
            coroutineScope.launch {
                bottomSheetState.partialExpand()
                showSelectRegionInfo = false
                bottomSheetState.expand()
            }
        } else {
            navigateUp(RESULT_CANCELED)
        }
    }

    CenteringBottomSheetScaffold(
        modifier = modifier,
        bottomSheetModifier = Modifier
            .padding(
                start = MaterialTheme.spacing.medium,
                end = MaterialTheme.spacing.medium,
                bottom = MaterialTheme.spacing.medium
            )
            .verticalScroll(rememberScrollState())
            .onGloballyPositioned {
                sheetHeight = it.size.height
            },
        topBar = {
            SuuntoTopBar(
                title = region.name,
                onNavigationClick = { navigateUp(RESULT_CANCELED) },
                actions = {
                    if (region.downloaded) {
                        SuuntoIconButton(
                            icon = SuuntoIcons.MoreVertical,
                            onClick = { menuExpanded = true },
                        )

                        DropdownMenu(
                            expanded = menuExpanded,
                            onDismissRequest = { menuExpanded = false },
                            modifier = Modifier.widthIn(min = 150.dp),
                            containerColor = MaterialTheme.colorScheme.surface,
                        ) {
                            DropdownMenuItem(
                                text = { Text(text = stringResource(id = R.string.about_osm_maps)) },
                                onClick = onOsmDisclaimer,
                            )

                            // TODO 187306: only show the item if downloaded to that target already

                            DropdownMenuItem(
                                text = { Text(text = stringResource(id = R.string.delete_map_from_watch)) },
                                onClick = { onDeleteRegion(OfflineMapDownloadTarget.WATCH) },
                            )

                            if (supportsOfflineMapsOnMobile) {
                                DropdownMenuItem(
                                    text = { Text(text = stringResource(id = R.string.delete_map_from_mobile)) },
                                    onClick = { onDeleteRegion(OfflineMapDownloadTarget.MOBILE) },
                                )
                            }
                        }
                    } else {
                        SuuntoIconButton(
                            icon = SuuntoIcons.Info,
                            onClick = onOsmDisclaimer,
                        )
                    }
                }
            )
        },
        scaffoldState = rememberBottomSheetScaffoldState(
            bottomSheetState = bottomSheetState,
        ),
        sheetPeekHeight = 32.dp,
        sheetContent = {
            if (showSelectRegionInfo) {
                SelectRegionsInfoSheet()
            } else {
                OfflineRegionSheetContent(
                    region = region,
                    supportsOfflineMapsOnMobile = supportsOfflineMapsOnMobile,
                    selectedDownloadTargets = selectedDownloadTargets,
                    onDownloadTargetSelectionChanged = onDownloadTargetSelectionChanged,
                    onDownloadRegion = onDownloadRegion,
                    selectedChinaRegions = selectedChinaRegions,
                    onSelectRegionInfo = {
                        coroutineScope.launch {
                            bottomSheetState.partialExpand()
                            showSelectRegionInfo = true
                            bottomSheetState.expand()
                        }
                    }
                )
            }
        }
    ) { internalPadding ->
        ContentCenteringColumn(Modifier.padding(internalPadding)) {
            Surface {
                if (watchStorageFull) {
                    InfoDialog(
                        title = stringResource(id = R.string.download_request_failed_generic_text),
                        text = stringResource(id = R.string.watch_storage_full),
                        confirmButtonText = stringResource(R.string.ok),
                        onDismissRequest = onWatchStorageFullDialogDismissRequest,
                        onConfirm = onWatchStorageFullDialogDismissRequest,
                    )
                }
                LaunchedEffect(key1 = Unit) {
                    delay(500) // Give time for navigation animation to complete
                    bottomSheetState.expand()
                }
                SuuntoMap(
                    modifier = Modifier
                        .fillMaxSize()
                        .onGloballyPositioned {
                            mapHeight = it.size.height
                        },
                    mapOptions = SuuntoMapOptions(
                        mapsProvider = MapboxMapsProvider.NAME,
                        uiAttribution = false
                    )
                ) { map ->
                    // Move Mapbox logo to top right corner
                    map.getUiSettings().setLogoPosition(Gravity.TOP or Gravity.END)
                    if (allChinaRegionsAndSelectState.isNotEmpty()) {
                        if (!region.boundaryUrl.isNullOrBlank()) {
                            MapHelper.addChinaOfflineRegionOverlay(
                                map,
                                region.id,
                                region.boundaryUrl,
                                ContextCompat.getColor(context, R.color.suunto_blue),
                            )
                        }
                        if (!region.maskUrl.isNullOrBlank()) {
                            MapHelper.addOfflineRegionMaskOverlay(map, region.maskUrl)
                        }

                        val layerIds = mutableListOf<String>()
                        val allChinaOfflineRegions = mutableListOf<SuuntoChinaOfflineRegion>()
                        val regionMarkers = mutableListOf<SuuntoMarkerOptions>()
                        allChinaRegionsAndSelectState.forEachIndexed { _, value ->
                            if (!value.region.boundaryUrl.isNullOrBlank()) {
                                val addChinaOfflineRegionOverlay =
                                    MapHelper.addChinaOfflineRegionOverlay(
                                        map,
                                        value.region.id,
                                        value.region.boundaryUrl,
                                        if (value.region.downloadAvailable) {
                                            ContextCompat.getColor(context, R.color.near_white)
                                        } else {
                                            ContextCompat.getColor(context, R.color.cloudy_grey)
                                        },
                                    )
                                value.region.centerPoint?.let {
                                    val icon =
                                        if (value.region.downloadRequested || value.region.downloading) {
                                            SuuntoBitmapResourceDescriptor(
                                                context,
                                                R.drawable.ic_downloading,
                                                true
                                            )
                                        } else if (value.region.downloaded) {
                                            SuuntoBitmapResourceDescriptor(
                                                context,
                                                R.drawable.ic_downloaded,
                                                true
                                            )
                                        } else {
                                            null
                                        }
                                    icon?.apply {
                                        regionMarkers.add(
                                            SuuntoMarkerOptions().position(it).icon(icon)
                                        )
                                    }
                                }
                                allChinaOfflineRegions.add(addChinaOfflineRegionOverlay)
                                val mask = addChinaOfflineRegionOverlay.mask
                                if (value.region.downloadAvailable && mask != null) {
                                    layerIds.add(mask.options.layerTypeOptions.getLayerId())
                                }
                            }
                        }
                        val markers = map.addMarkers(regionMarkers)
                        onLoadAllChinaOfflineRegionSuccess.invoke(allChinaOfflineRegions, markers)
                        map.addOnMapClickListener(object : SuuntoMap.OnMapClickListener {
                            override fun onMapClick(latLng: LatLng, placeName: String?) {
                                coroutineScope.launch {
                                    val clickLayerSourceIds = map.getClickLayerSourceIds(
                                        latLng,
                                        layerIds
                                    )
                                    if (clickLayerSourceIds.isNotEmpty()) {
                                        onSelectRegion.invoke(clickLayerSourceIds.first())
                                    }
                                }
                            }
                        })
                        map.addOnScaleListener(object : SuuntoMap.OnScaleListener {
                            override fun onScaleBegin() {
                                // do nothing
                            }

                            override fun onScaleEnd() {
                                // scale markers
                                onMapScaleListener(map.getCameraPosition()?.zoom ?: 1f)
                            }
                        })
                    } else {
                        if (!region.maskUrl.isNullOrBlank()) {
                            MapHelper.addOfflineRegionMaskOverlay(map, region.maskUrl)
                        }
                        if (!region.boundaryUrl.isNullOrBlank()) {
                            MapHelper.addOfflineRegionOverlay(
                                map,
                                region.boundaryUrl,
                                region.id,
                                ContextCompat.getColor(context, R.color.suunto_blue)
                            )
                        }
                    }
                    setCamera(region, map, mapPadding, sheetHeight, mapHeight)
                }
            }
        }
    }
}

private fun setCamera(
    region: OfflineRegionResult.OfflineRegion,
    map: SuuntoMap?,
    mapPadding: Int,
    sheetHeight: Int,
    mapHeight: Int,
) {
    if (map == null || region.bounds == null) return

    val bottomPadding = if (mapHeight < sheetHeight) mapPadding else sheetHeight
    map.setPadding(mapPadding, mapPadding, mapPadding, bottomPadding)
    map.moveCamera(
        update = SuuntoCameraUpdateNewLatLngBounds(
            bounds = region.bounds,
            padding = mapPadding,
        )
    )
}

@Composable
private fun SelectRegionsInfoSheet(
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
    ) {
        Text(
            text = stringResource(id = R.string.default_selected_region),
            modifier = Modifier.padding(
                top = MaterialTheme.spacing.small,
                bottom = MaterialTheme.spacing.large,
            ),
            style = MaterialTheme.typography.bodyXLargeBold,
        )

        Text(
            text = stringResource(id = R.string.adjacent_region_content),
            style = MaterialTheme.typography.bodyLarge,
        )
    }
}

@Composable
private fun OfflineRegionSheetContent(
    region: OfflineRegionResult.OfflineRegion,
    supportsOfflineMapsOnMobile: Boolean,
    selectedDownloadTargets: ImmutableSet<OfflineMapDownloadTarget>,
    onDownloadTargetSelectionChanged: (OfflineMapDownloadTarget, Boolean) -> Unit,
    onDownloadRegion: (OfflineRegionResult.OfflineRegion) -> Unit,
    onSelectRegionInfo: () -> Unit,
    modifier: Modifier = Modifier,
    selectedChinaRegions: ImmutableList<OfflineRegionResult.OfflineRegion> = persistentListOf(),
) {
    Column(modifier = modifier) {
        if (region.batchDownloadAllowed == true) {
            ChinaOfflineRegionInfo(
                currentRegion = region,
                selectedChinaRegions = selectedChinaRegions,
                onSelectRegionInfo = onSelectRegionInfo,
            )
        }

        OfflineMapValues(
            supportsOfflineMapsOnMobile = supportsOfflineMapsOnMobile,
            currentRegion = region,
            modifier = Modifier.padding(top = MaterialTheme.spacing.small),
            selectedChinaRegions = selectedChinaRegions,
        )

        // TODO 187306: What to do if downloaded to only one target?

        if (supportsOfflineMapsOnMobile && region.downloadAvailable) {
            Spacer(Modifier.height(MaterialTheme.spacing.xlarge))

            DownloadTargetToggle(
                icon = SuuntoIcons.WatchOutline,
                title = R.string.offline_maps_download_target_watch_title,
                description = R.string.offline_maps_download_target_watch_description,
                selected = selectedDownloadTargets.contains(OfflineMapDownloadTarget.WATCH),
                onSelectChanged = { selected -> onDownloadTargetSelectionChanged(OfflineMapDownloadTarget.WATCH, selected) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = MaterialTheme.spacing.medium),
            )

            HorizontalDivider()

            DownloadTargetToggle(
                icon = SuuntoIcons.PhoneOutline,
                title = R.string.offline_maps_download_target_mobile_title,
                description = R.string.offline_maps_download_target_mobile_description,
                selected = selectedDownloadTargets.contains(OfflineMapDownloadTarget.MOBILE),
                onSelectChanged = { selected -> onDownloadTargetSelectionChanged(OfflineMapDownloadTarget.MOBILE, selected) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = MaterialTheme.spacing.medium),
            )

            HorizontalDivider()
        }

        Spacer(Modifier.height(MaterialTheme.spacing.xlarge))

        Row(
            modifier = Modifier
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = SuuntoIcons.Info.asPainter(),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.medium),
            )

            Text(
                text = stringResource(R.string.offline_maps_styles_tip),
                style = MaterialTheme.typography.body,
                modifier = Modifier
                    .padding(start = MaterialTheme.spacing.medium),
            )
        }

        Spacer(Modifier.height(MaterialTheme.spacing.xlarge))

        if (region.downloadAvailable) {
            PrimaryButton(
                onClick = { onDownloadRegion(region) },
                modifier = Modifier
                    .fillMaxWidth(),
                enabled = !supportsOfflineMapsOnMobile || selectedDownloadTargets.isNotEmpty(),
            ) {
                Icon(
                    painter = painterResource(R.drawable.download_fill),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onPrimary,
                )
                Spacer(
                    modifier = Modifier.size(MaterialTheme.spacing.small)
                )
                Text(
                    text = stringResource(R.string.offline_maps_download_map)
                        .uppercase(Locale.getDefault()),
                    color = MaterialTheme.colorScheme.onPrimary,
                )
            }
        }
    }
}

@Composable
private fun DownloadTargetToggle(
    icon: SuuntoIcon,
    @StringRes title: Int,
    @StringRes description: Int,
    selected: Boolean,
    onSelectChanged: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = icon.asPainter(),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.small),
        )

        Spacer(Modifier.width(MaterialTheme.spacing.medium))

        Column {
            Text(
                text = stringResource(title),
                color = MaterialTheme.colorScheme.onSurface,
                style = MaterialTheme.typography.bodyLarge,
            )

            Text(
                text = stringResource(description),
                color = MaterialTheme.colorScheme.darkGrey,
                style = MaterialTheme.typography.bodyMedium,
            )
        }

        Spacer(Modifier.weight(1.0F))

        SuuntoSwitch(
            checked = selected,
            onCheckedChange = onSelectChanged,
        )
    }
}

@SuppressLint("InlinedApi")
@Composable
fun OfflineMapValues(
    supportsOfflineMapsOnMobile: Boolean,
    currentRegion: OfflineRegionResult.OfflineRegion,
    modifier: Modifier = Modifier,
    selectedChinaRegions: ImmutableList<OfflineRegionResult.OfflineRegion> = persistentListOf(),
) {
    val context = LocalContext.current

    val (fileSizeForWatch, fileSizeUnitForWatch) = context.formatFileSize(
        size = (currentRegion.sizeForWatch?.storageSizeInBytes ?: 0L) + selectedChinaRegions.sumOf { it.sizeForWatch?.storageSizeInBytes ?: 0L },
    )
    val (fileSizeForMobile, fileSizeUnitForMobile) = context.formatFileSize(
        size = (currentRegion.sizeForMobile?.storageSizeInBytes ?: 0L) + selectedChinaRegions.sumOf { it.sizeForMobile?.storageSizeInBytes ?: 0L },
    )

    Box(modifier = modifier.fillMaxWidth()) {
        val regionAreaSize = (currentRegion.area ?: 0.0) + selectedChinaRegions.sumOf { it.area ?: 0.0 }
        val regionAreaUnitResId = currentRegion.areaUnitRes ?: Resources.ID_NULL
        if (regionAreaSize != 0.0 && regionAreaUnitResId != Resources.ID_NULL) {
            MapValue(
                value = String.format(Locale.ROOT, "%.1f", regionAreaSize),
                unit = stringResource(id = regionAreaUnitResId),
                label = stringResource(id = R.string.area_size)
            )
        }

        if (supportsOfflineMapsOnMobile && fileSizeForMobile.isNotBlank()) {
            MapValue(
                value = fileSizeForMobile,
                unit = fileSizeUnitForMobile,
                label = stringResource(id = R.string.in_mobile_file_size),
                modifier = Modifier.align(Alignment.Center),
            )
        }

        if (fileSizeForWatch.isNotBlank()) {
            MapValue(
                value = fileSizeForWatch,
                unit = fileSizeUnitForWatch,
                label = stringResource(id = R.string.in_watch_file_size),
                modifier = Modifier.align(Alignment.CenterEnd),
            )
        }
    }
}

private fun Context.formatFileSize(size: Long): Pair<String, String> {
    if (size <= 0) return "" to ""

    val fields = Formatter.formatShortFileSize(this, size).split(' ')
    return fields.first() to fields.last()
}

@Composable
private fun MapValue(
    value: String,
    unit: String,
    label: String,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxsmall),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Row {
            Text(
                text = value,
                style = MaterialTheme.typography.numbers2,
                modifier = Modifier.alignByBaseline()
            )
            Text(
                text = unit,
                style = MaterialTheme.typography.body,
                modifier = Modifier
                    .alignByBaseline()
                    .padding(start = MaterialTheme.spacing.xsmall)
            )
        }

        Text(
            text = label,
            style = MaterialTheme.typography.body,
        )
    }
}

@Composable
private fun ChinaOfflineRegionInfo(
    currentRegion: OfflineRegionResult.OfflineRegion,
    selectedChinaRegions: ImmutableList<OfflineRegionResult.OfflineRegion>,
    onSelectRegionInfo: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = stringResource(id = R.string.default_selected_region)
                    .uppercase(Locale.getDefault()),
                style = MaterialTheme.typography.bodySmallBold,
            )

            SuuntoIconButton(
                icon = SuuntoIcons.Info,
                onClick = onSelectRegionInfo,
                tint = MaterialTheme.colorScheme.darkGrey,
            )
        }

        Row(
            modifier = Modifier
                .padding(bottom = MaterialTheme.spacing.medium)
                .horizontalScroll(rememberScrollState()),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        ) {
            ChinaRegionChip(
                selectedRegion = currentRegion,
            )

            selectedChinaRegions.forEach {
                ChinaRegionChip(
                    selectedRegion = it,
                )
            }
        }
    }
}

@Composable
private fun ChinaRegionChip(
    selectedRegion: OfflineRegionResult.OfflineRegion,
    modifier: Modifier = Modifier
) {
    FilterChip(
        selected = true,
        onClick = { /* Do nothing */ },
        label = {
            Text(
                text = stringResource(
                    id = R.string.offline_maps_region_name,
                    selectedRegion.name.takeLast(2),
                ),
                style = MaterialTheme.typography.body,
            )
        },
        modifier = modifier,
        shape = RoundedCornerShape(percent = 50),
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = MaterialTheme.colorScheme.primary,
            selectedLabelColor = MaterialTheme.colorScheme.onPrimary,
        ),
    )
}

@Preview(showBackground = true)
@Composable
private fun SelectRegionsInfoSheetPreview() {
    M3AppTheme {
        SelectRegionsInfoSheet(
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
        )
    }
}

@Preview
@Composable
private fun OfflineRegionPreviewPreview() {
    M3AppTheme {
        OfflineRegionPreviewScreen(
            region = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS
                .first { it.name == "Finland" }.regions.first(),
            supportsOfflineMapsOnMobile = true,
            selectedDownloadTargets = persistentSetOf(),
            onDownloadTargetSelectionChanged = { _, _ -> },
            onDownloadRegion = {},
            onOsmDisclaimer = {},
            onDeleteRegion = {},
            navigateUp = {},
            onSelectRegion = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun OfflineRegionBottomSheetPreview() {
    M3AppTheme {
        OfflineRegionSheetContent(
            region = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS
                .first { it.name == "Finland" }
                .regions
                .first { it.downloadAvailable },
            supportsOfflineMapsOnMobile = true,
            selectedDownloadTargets = persistentSetOf(OfflineMapDownloadTarget.MOBILE),
            onDownloadTargetSelectionChanged = { _, _ -> },
            onDownloadRegion = {},
            onSelectRegionInfo = {},
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
        )
    }
}
