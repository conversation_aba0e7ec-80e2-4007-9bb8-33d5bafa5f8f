package com.stt.android.offlinemaps.ui

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.LinkAnnotation
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing

internal enum class LinkTarget {
    WIFI_SETUP,
    LIBRARY,
}

@Composable
internal fun DownloadInfoSheet(
    onDownloadInfoLink: (LinkTarget) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .padding(MaterialTheme.spacing.medium),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.large),
    ) {
        Text(
            text = stringResource(id = R.string.dl_info_title),
            style = MaterialTheme.typography.bodyXLargeBold,
        )

        DownloadInfoSheetLinkItem(
            imageRes = R.drawable.dl_info_wifi,
            textRes = R.string.dl_info_wifi_format_string,
            linkRes = R.string.dl_info_wifi_format_param,
            linkTarget = LinkTarget.WIFI_SETUP,
            onDownloadInfoLink = onDownloadInfoLink,
        )

        DownloadInfoSheetItem(
            imageRes = R.drawable.dl_info_download,
            textRes = R.string.dl_info_download,
        )

        DownloadInfoSheetItem(
            imageRes = R.drawable.dl_info_plug_charger,
            textRes = R.string.dl_info_plug_charger,
        )

        DownloadInfoSheetItem(
            imageRes = R.drawable.dl_info_charger,
            textRes = R.string.dl_info_charger,
        )

        DownloadInfoSheetItem(
            imageRes = R.drawable.dl_info_download_time,
            textRes = R.string.dl_info_download_time,
        )

        DownloadInfoSheetLinkItem(
            imageRes = R.drawable.dl_info_library,
            textRes = R.string.dl_info_library_format_string,
            linkRes = R.string.dl_info_library_format_param,
            linkTarget = LinkTarget.LIBRARY,
            onDownloadInfoLink = onDownloadInfoLink,
        )
    }
}

@Composable
private fun DownloadInfoSheetItem(
    @DrawableRes imageRes: Int,
    @StringRes textRes: Int,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        val text = stringResource(textRes)

        Image(
            painter = painterResource(imageRes),
            contentDescription = text,
        )

        Spacer(Modifier.width(MaterialTheme.spacing.medium))

        Text(
            text = text,
            style = MaterialTheme.typography.bodyLarge,
        )
    }
}

@Composable
private fun DownloadInfoSheetLinkItem(
    @DrawableRes imageRes: Int,
    @StringRes textRes: Int,
    @StringRes linkRes: Int,
    linkTarget: LinkTarget,
    onDownloadInfoLink: (LinkTarget) -> Unit,
    modifier: Modifier = Modifier,
) {
    val annotatedText = buildAnnotatedString {
        val linkText = stringResource(id = linkRes)
        val text = stringResource(id = textRes, linkText)
        val offset = text.indexOf(linkText)
        append(text)
        addLink(
            clickable = LinkAnnotation.Clickable("") {
                onDownloadInfoLink(linkTarget)
            },
            start = offset,
            end = offset + linkText.length,
        )
        addStyle(
            style = SpanStyle(
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary,
            ),
            start = offset,
            end = offset + linkText.length,
        )
    }

    Row(
        modifier = modifier.wrapContentHeight(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = imageRes),
            contentDescription = annotatedText.toString(),
        )

        Text(
            text = annotatedText,
            modifier = Modifier
                .wrapContentHeight()
                .padding(start = MaterialTheme.spacing.medium),
            style = MaterialTheme.typography.bodyLarge,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun DownloadInfoSheetPreview() {
    M3AppTheme {
        DownloadInfoSheet(
            onDownloadInfoLink = {},
        )
    }
}
