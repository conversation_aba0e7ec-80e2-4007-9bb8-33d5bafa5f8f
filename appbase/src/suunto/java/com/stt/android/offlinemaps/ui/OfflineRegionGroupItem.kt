package com.stt.android.offlinemaps.ui

import android.text.format.Formatter.formatShortFileSize
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.highlightedString
import com.stt.android.offlinemaps.datasource.DummyOfflineRegionDataSource
import com.stt.android.offlinemaps.entity.OfflineRegionResult

@Composable
internal fun OfflineRegionGroupItem(
    group: OfflineRegionResult.OfflineRegionGroup,
    highlight: String?,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .clickable(onClick = onClick)
            .padding(MaterialTheme.spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Icon(
            painter = painterResource(R.drawable.folder_outline),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.medium),
            tint = MaterialTheme.colorScheme.secondary,
        )

        Spacer(Modifier.width(MaterialTheme.spacing.medium))

        Column {
            Text(
                text = highlightedString(
                    text = group.name,
                    highlight = highlight,
                    highlightStyle = SpanStyle(
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Bold,
                    )
                ),
                style = MaterialTheme.typography.bodyLarge,
            )

            Text(
                text = groupSecondaryText(group),
                style = MaterialTheme.typography.bodyMedium,
            )
        }

        Spacer(Modifier.weight(1.0F))

        Icon(
            painter = SuuntoIcons.ActionRight.asPainter(),
            contentDescription = null,
            modifier = Modifier.size(MaterialTheme.iconSizes.medium),
            tint = MaterialTheme.colorScheme.secondary,
        )
    }
}

@Composable
private fun groupSecondaryText(group: OfflineRegionResult.OfflineRegionGroup): String =
    buildString {
        append(
            pluralStringResource(
                R.plurals.offline_region_count,
                group.regions.size,
                group.regions.size
            )
        )
        append(" | ")
        // TODO: What format exactly should the size have? SI (base 1000) or IEC (1024)?
        // What should the precision be?
        append(formatShortFileSize(LocalContext.current, group.size))
    }

@Preview(showBackground = true)
@Composable
private fun OfflineRegionGroupItemPreview() {
    M3AppTheme {
        Column {
            OfflineRegionGroupItem(
                group = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS[2],
                highlight = null,
                onClick = {},
            )

            HorizontalDivider()

            OfflineRegionGroupItem(
                group = DummyOfflineRegionDataSource.DUMMY_OFFLINE_REGION_GROUPS[2],
                highlight = "Fin",
                onClick = {},
            )
        }
    }
}
