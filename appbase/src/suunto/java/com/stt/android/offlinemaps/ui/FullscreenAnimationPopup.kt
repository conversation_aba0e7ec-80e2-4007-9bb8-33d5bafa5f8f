package com.stt.android.offlinemaps.ui

import androidx.annotation.StringRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.MutableTransitionState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.stt.android.R
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.core.utils.EventThrottler
import com.stt.android.core.utils.onClick
import java.util.Locale

@Composable
fun FullscreenAnimationPopup(
    visible: Boolean,
    onBatteryTipDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    @StringRes titleRes: Int = R.string.setup_wifi_battery_tip_title,
    @StringRes textRes: Int = R.string.setup_wifi_battery_tip_text,
    assetFileName: String = "download_maps_animation.json",
) {
    val transitionState = remember { MutableTransitionState(false) }
    val eventThrottler = remember { EventThrottler() }
    var alpha by rememberSaveable { mutableFloatStateOf(0f) }

    transitionState.targetState = visible

    if (!transitionState.currentState && !transitionState.targetState && transitionState.isIdle) {
        return
    }

    Popup(
        onDismissRequest = onBatteryTipDismiss,
        properties = PopupProperties(usePlatformDefaultWidth = false),
    ) {
        AnimatedVisibility(
            visibleState = transitionState,
            enter = fadeIn(animationSpec = tween()),
            exit = fadeOut(animationSpec = tween()),
            modifier = modifier,
        ) {
            Surface(
                modifier = Modifier
                    .fillMaxSize()
                    .narrowContent(),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = MaterialTheme.spacing.medium)
                        // Setting items visible only after the lottie animation has loaded itself making UI less busy
                        .alpha(alpha),
                ) {
                    AnimationComposition(
                        assetFileName = assetFileName,
                        modifier = Modifier
                            .weight(weight = 1f, fill = false)
                            .fillMaxWidth()
                            .alpha(alpha),
                        onAnimLoaded = { alpha = 1f },
                    )
                    Column(
                        modifier = Modifier
                            .wrapContentHeight()
                            .padding(vertical = MaterialTheme.spacing.medium),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        Text(
                            text = stringResource(titleRes)
                                .uppercase(Locale.getDefault()),
                            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.xxlarge),
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.bodyBold,
                        )

                        Text(
                            text = stringResource(textRes),
                            modifier = Modifier
                                .padding(top = MaterialTheme.spacing.smaller),
                            textAlign = TextAlign.Center,
                            style = MaterialTheme.typography.bodyLarge,
                        )

                        PrimaryButton(
                            text = stringResource(R.string.ok),
                            onClick = eventThrottler.onClick(onBatteryTipDismiss),
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(
                                    top = MaterialTheme.spacing.large,
                                    bottom = MaterialTheme.spacing.medium,
                                ),
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun AnimationComposition(
    assetFileName: String,
    onAnimLoaded: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val composition = rememberLottieComposition(
        spec = LottieCompositionSpec.Asset(assetFileName),
        imageAssetsFolder = "images",
    )
    LottieAnimation(
        composition = composition.value,
        modifier = modifier,
        iterations = LottieConstants.IterateForever,
    )

    if (composition.isComplete) {
        LaunchedEffect(key1 = Unit) {
            onAnimLoaded()
        }
    }
}
