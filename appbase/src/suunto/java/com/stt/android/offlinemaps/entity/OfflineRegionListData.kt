package com.stt.android.offlinemaps.entity

import androidx.compose.runtime.Immutable
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

sealed class OfflineRegionListData {
    @Immutable
    data class Group(
        val group: OfflineRegionResult.OfflineRegionGroup,
    ) : OfflineRegionListData()

    @Immutable
    data class Catalogue(
        val nearby: ImmutableList<OfflineRegionResult> = persistentListOf(),
        val groups: ImmutableList<OfflineRegionResult.OfflineRegionGroup> = persistentListOf(),
    ) : OfflineRegionListData()
}
